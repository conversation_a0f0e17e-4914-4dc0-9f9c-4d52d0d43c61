const robot = require("robotjs");
const {ipcMain} = require("electron");
const axios = require("axios");

// EXAMPLE
const commandsExample = [
    { type: "toggle", key: "control", action: "down" },
    { type: "tap", key: "a" },
    { type: "wait", duration: 1000 }, // Wait for 1000 milliseconds
    { type: "tap", key: "c" },
    { type: "shakeMouse", duration: 1000,  intensity: 5},
    { type: "moveMouse", x: 10,  y: 10},
    { type: "toggle", key: "control", action: "up" },
    { type: "wait", duration: 500 }, // Wait for 500 milliseconds
    { type: "mouseClick", button: "left" }, // Simulate left mouse click
    { type: "mouseClick", button: "right" }, // Simulate right mouse click
    { type: "mouseClick", button: "middle" }, // Simulate right mouse click
    { type: "mouseScroll", x: 0, y:100 }, // Simulate right mouse click
    { type: "typeString", string: "Text to type" },
    { type: "pressAndHoldKey", key: "shift", duration: 2000 }, // Hold 'shift' for 2000 milliseconds
    { type: "pressAndHoldMouse", key: "left", duration: 2000 } // Hold 'shift' for 2000 milliseconds
];

function shakeMouse(duration, intensity) {
    const startTime = Date.now();
    const currentPosition = robot.getMousePos();

    function shake() {
        const elapsedTime = Date.now() - startTime;
        if (elapsedTime > duration) {
            console.log("Mouse shake completed.");
            return;
        }
        const newX = currentPosition.x + ((Math.random() * 2  - 1)  * intensity);
        const newY = currentPosition.y + ((Math.random() * 2  - 1)  * intensity);
        robot.moveMouse(newX, newY);
        setTimeout(shake, 100); // Adjust the frequency of movement by changing the timeout duration
    }

    shake();
}

function moveMouse(x, y) {
    const currentPosition = robot.getMousePos();
    const newX = currentPosition.x + x;
    const newY = currentPosition.y + y;
    robot.moveMouse(newX, newY);
}

function executeCommands(commandList) {
    let promise = Promise.resolve();

    commandList.forEach(command => {
        promise = promise.then(async () => {
            if(command?.delay){
                await wait(command.delay);
            }

            if(command?.modifier === 'undefined'){
                command.modifier = undefined;
            }

            switch (command.type) {
                case "tap":
                    //console.log('tap: ', command.key, command?.modifier);
                    if( command?.modifier) {
                        robot.keyTap(command.key, command?.modifier);
                    } else {
                        robot.keyTap(command.key);
                    }
                    break;
                case "moveMouse":
                    moveMouse(command.x, command.y);
                    break;
                case "shakeMouse":
                    shakeMouse(command.duration, command.intensity);
                    break;
                case "toggle":
                    if( command?.modifier) {
                        robot.keyToggle(command.key, command.action, command?.modifier);
                    } else {
                        robot.keyToggle(command.key, command.action);
                    }
                    break;
                case "typeString":
                    robot.typeString(command.string);
                    break;
                case "mouseClick":
                    robot.mouseClick(convertMouseCommand(command.key), command?.double);
                    break;
                // case "mouseScroll":
                //     robot.scrollMouse(command.x, command.y);
                //     break;
                case "wait":
                    await wait(command.duration);
                case "pressAndHoldKey":
                    if( command?.modifier) {
                        robot.keyToggle(command.key, 'down', command?.modifier);
                    } else {
                        robot.keyToggle(command.key, 'down');
                    }
                    setTimeout(() => {
                        if( command?.modifier) {
                            robot.keyToggle(command.key, 'up', command?.modifier);
                        } else {
                            robot.keyToggle(command.key, 'up');
                        }
                    }, command.duration);
                    break;
                case "pressAndHoldMouse":
                    const cmd = convertMouseCommand(command.key);
                    robot.mouseToggle('down', cmd);
                    setTimeout(() => {
                        robot.mouseToggle('up', cmd);
                    }, command.duration);
                    break;
                default:
                    console.log("Unknown command type");
            }
        });
    });

    return promise;
}

function convertMouseCommand(key) {
    return key.startsWith('mouse_') ? key.slice('mouse_'.length) : key;
}

function wait(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

function registerKeyboardEventHandler(){
    ipcMain.on('keyboardSimulator', (event, args) => {
        try {
            //console.log('keyboardSimulator ', args?.jsonCommand);
            const jsonCommand = JSON.parse(args?.jsonCommand);
            executeCommands(jsonCommand);
        } catch (e) {
            console.error("Error parsing keyboardSimulator JSON command!", e);
        }
    });
}

module.exports = {executeCommands, registerKeyboardEventHandler};


// commands examples

// const straifLeft = [
//     { type: "pressAndHoldKey", key: "a", duration: 3000 },
//     { type: "wait", duration: 500 },
//     { type: "tap", key: "d" },
// ]
//
// const straifRight = [
//     { type: "pressAndHoldKey", key: "d", duration: 3000 },
//     { type: "wait", duration: 500 },
//     { type: "tap", key: "a" },
// ]
//
//
// const mouseUp = [
//     { type: "moveMouse", x: 0,  y: -1000},
// ]
//
// const mouseDown = [
//     { type: "moveMouse", x: 0,  y: 1000},
// ]
//
// const turnBack = [
//     { type: "moveMouse", x: 1000,  y: 0},
// ]
//
// const mouseLeftX2 = [
//     { type: "mouseClick", button: "left" },
//     { type: "wait", duration: 500 },
//     { type: "mouseClick", button: "left" }
// ]
//
// const mouseRight = [
//     { type: "mouseClick", button: "right" },
// ]
//
// const knife = [
//     { type: "tap", key: "3" },
//     { type: "wait", duration: 500 },
//     { type: "mouseClick", button: "right" }
// ]
//
// const drop = [
//     { type: "tap", key: "g" },
// ]
//
// const gg = [
//     { type: "tap", key: "g" },
//     { type: "wait", duration: 500 },
//     { type: "tap", key: "g" }
// ]
//
// const jump = [
//     { type: "pressAndHoldKey", key: "w", duration: 3500 },
//     { type: "wait", duration: 500 },
//     { type: "tap", key: "space" },
//     { type: "wait", duration: 1000 },
//     { type: "tap", key: "space" },
//     { type: "wait", duration: 1000 },
//     { type: "tap", key: "space" },
//     { type: "wait", duration: 1000 },
//     { type: "tap", key: "space" },
// ]
//
// const duck = [
//     { type: "tap", key: "control" },
//     { type: "wait", duration: 600 },
//     { type: "tap", key: "control" },
//     { type: "wait", duration: 800 },
//     { type: "tap", key: "control" },
//     { type: "wait", duration: 1000 },
//     { type: "tap", key: "control" },
// ]
//
// const leftMouseLongClick = [
//     { type: "wait", duration: 3000 },
//     { type: "pressAndHoldMouse", key: "left", duration: 3000 }
// ]


// ROBOT js library hotfix
//
// ADD this function
// bool isExtendedKey(int key) {
//     switch (key) {
//         case VK_UP:
//         case VK_DOWN:
//         case VK_LEFT:
//         case VK_RIGHT:
//         case VK_INSERT:
//         case VK_HOME:
//         case VK_END:
//         case VK_PRIOR:
//         case VK_NEXT:
//         case VK_PAUSE:
//         case VK_PRINT:
//         case VK_LWIN:
//         case VK_RWIN:
//         case VK_NUMLOCK:
//         case VK_SCROLL:
//         case VK_LMENU:
//         case VK_RMENU:
//         case VK_LCONTROL:
//         case VK_RCONTROL:
//             return true;  // These are extended keys
//         default:
//             return false; // Not an extended key
//     }
// }
//
// CHANGE THIS IF STATMENT
// /* Set the scan code for keyup */
// if ( flags & KEYEVENTF_KEYUP ) {
//     if (isExtendedKey(key)) {
//         scan |= 0x80;
//     }
// }