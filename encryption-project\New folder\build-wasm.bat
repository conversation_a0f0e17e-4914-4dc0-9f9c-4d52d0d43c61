@echo off
echo 🔧 Building WASM module...

REM Check if emcc is available
where emcc >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo ❌ Emscripten not found!
    echo Please install Emscripten first:
    echo https://emscripten.org/docs/getting_started/downloads.html
    pause
    exit /b 1
)

echo ✅ Emscripten found, compiling...

REM Compile C to WASM
emcc decrypt.c -o decrypt.js ^
    -s EXPORTED_FUNCTIONS="['_decrypt_data', '_free_memory', '_malloc', '_free']" ^
    -s EXPORTED_RUNTIME_METHODS="['ccall', 'cwrap', 'UTF8ToString', 'stringToUTF8']" ^
    -s ALLOW_MEMORY_GROWTH=1 ^
    -s MODULARIZE=1 ^
    -s EXPORT_NAME="DecryptModule" ^
    -O2

if %ERRORLEVEL% equ 0 (
    echo ✅ WASM module built successfully!
    echo 📁 Files created:
    echo   - decrypt.js
    echo   - decrypt.wasm
    echo.
    echo 🎯 Next: Test the WASM module
) else (
    echo ❌ Build failed!
)

pause