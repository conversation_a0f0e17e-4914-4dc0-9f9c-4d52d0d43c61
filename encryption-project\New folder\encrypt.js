const crypto = require("crypto");
const fs = require("fs");

console.log('🔐 بدء عملية تشفير index.js...');

// التحقق من وجود الملف
if (!fs.existsSync("index.js")) {
  console.error('❌ ملف index.js غير موجود!');
  process.exit(1);
}

// إنشاء مفتاح وIV عشوائيين
const key = crypto.randomBytes(32); // 256 bit key
const iv = crypto.randomBytes(16);  // 128 bit IV

console.log('🔑 تم إنشاء مفتاح التشفير و IV');

// قراءة محتوى index.js
const data = fs.readFileSync("index.js", "utf-8");
console.log(`📄 تم قراءة index.js (${data.length} حرف)`);

// تشفير البيانات
const cipher = crypto.createCipheriv("aes-256-cbc", key, iv);
let encrypted = cipher.update(data, "utf8", "base64");
encrypted += cipher.final("base64");

console.log('🔒 تم تشفير البيانات بنجاح');

// حفظ الملفات
fs.writeFileSync("index.encrypted", encrypted);
fs.writeFileSync("key.txt", key.toString("hex"));
fs.writeFileSync("iv.txt", iv.toString("hex"));

console.log('✅ تم حفظ الملفات:');
console.log('  - index.encrypted (الملف المشفر)');
console.log('  - key.txt (مفتاح التشفير)');
console.log('  - iv.txt (IV)');
console.log('');
console.log('🎯 الخطوة التالية: تشغيل loader.js');