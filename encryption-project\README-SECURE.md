# نظام التشفير الآمن باستخدام WASM

## نظرة عامة
هذا النظام يوفر حماية متقدمة للكود والمفاتيح باستخدام WebAssembly (WASM) وتقنيات التشفير المتعددة.

## الملفات الجديدة

### 1. `secure-decrypt-fixed.c`
- يحتوي على المفاتيح المشفرة كثوابت في الكود
- يوفر دوال لفك تشفير المفاتيح والبيانات
- يتم بناؤه كـ WASM للاستخدام في JavaScript

### 2. `build-secure-wasm.bat`
- سكريبت لبناء ملف WASM من كود C
- يصدر الدوال اللازمة للاستخدام في JavaScript

### 3. `secure-loader.js`
- محمل آمن لفك تشفير وتشغيل الكود في الذاكرة
- يستخدم WASM لفك تشفير المفاتيح والبيانات
- يحتوي على خطة بديلة في حال فشل WASM

### 4. `encrypt-secure.js`
- يقوم بتشفير index.js وإنشاء المفاتيح
- ينشئ ملف C مع المفاتيح المشفرة
- يجهز كل ما يلزم لعملية التشفير الآمن

## طريقة الاستخدام

### 1. تشفير الكود والمفاتيح
```bash
node encrypt-secure.js
```

هذا الأمر سيقوم بـ:
- تشفير index.js وحفظه كـ index.encrypted
- إنشاء ملف secure-decrypt-fixed.c مع المفاتيح المشفرة
- طباعة الخطوات التالية

### 2. بناء WASM
```bash
build-secure-wasm.bat
```

هذا الأمر سيقوم بـ:
- بناء secure-decript.c كـ WASM
- إنشاء secure-decrypt.js و secure-decrypt.wasm

### 3. رفع الملفات على Cloudflare Workers
- ارفع index.encrypted على Cloudflare Workers
- تأكد من تحديث الرابط في secure-loader.js

### 4. تشغيل البرنامج
```bash
node secure-loader.js
```

هذا الأمر سيقوم بـ:
- تحميل WASM
- تحميل البيانات المشفرة من Cloudflare
- فك تشفير البيانات باستخدام المفاتيح من WASM
- تشغيل الكود في الذاكرة

## ميزات الأمان

### 1. حماية المفاتيح
- المفاتيح مشفرة ومدمجة في WASM
- استخدام مفتاح رئيسي لفك تشفير المفاتيح
- صعوبة استخراج المفاتيح من WASM

### 2. حماية الكود
- الكود الأصلي مشفر ومخزن على Cloudflare
- يتم تحميله وفك تشفيره في الذاكرة فقط
- لا يتم حفظ الكود غير المشفر على القرص

### 3. حماية التنفيذ
- يتم تنفيذ الكود في بيئة معزولة (VM)
- تغيير ديناميكي للمنفذ لتجنب التعارض
- خطة بديلة في حال فشل WASM

## التخصيص

### تغيير الروابط
في secure-loader.js، قم بتغيير:
```javascript
const url = 'https://your-worker.your-subdomain.workers.dev/index.encrypted';
```

### تغيير المنفذ
في secure-loader.js، قم بتغيير:
```javascript
const PORT = process.env.PORT || 3001;
```

## استكشاف الأخطاء

### مشاكل بناء WASM
- تأكد من تثبيت Emscripten SDK
- تحقق من وجود ملف secure-decrypt-fixed.c

### مشاكل التحميل من Cloudflare
- تحقق من رابط index.encrypted
- تأكد من أن الملف متاح للوصول العام

### مشاكل فك التشفير
- تأكد من بناء WASM بنجاح
- تحقق من تطابق المفاتيح في جميع الملفات

## الخلاصة
هذا النظام يوفر حماية متقدمة للكود والمفاتيح باستخدام تقنيات متعددة، مما يجعله مناسباً للتطبيقات التي تتطلب أماناً عالياً.
