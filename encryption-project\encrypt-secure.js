const crypto = require('crypto');
const fs = require('fs');

console.log('🔐 بدء عملية التشفير الآمن...');

// التحقق من وجود الملف
if (!fs.existsSync("index.js")) {
  console.error('❌ ملف index.js غير موجود!');
  process.exit(1);
}

// إنشاء مفاتيح عشوائية
const key = crypto.randomBytes(32); // 256 bit key
const iv = crypto.randomBytes(16);  // 128 bit IV
const masterKey = crypto.randomBytes(16); // 128 bit master key

console.log('🔑 تم إنشاء مفاتيح التشفير');

// قراءة محتوى index.js
const data = fs.readFileSync("index.js", "utf8");
console.log(`📄 تم قراءة index.js (${data.length} حرف)`);

// تشفير البيانات
const cipher = crypto.createCipheriv("aes-256-cbc", key, iv);
let encrypted = cipher.update(data, "utf8", "base64");
encrypted += cipher.final("base64");

console.log('🔒 تم تشفير البيانات بنجاح');

// تشفير المفاتيح باستخدام المفتاح الرئيسي
const keyCipher = crypto.createCipheriv("aes-128-ecb", masterKey, Buffer.alloc(0));
const encryptedKey = keyCipher.update(key, 'binary', 'hex') + keyCipher.final('hex');

const ivCipher = crypto.createCipheriv("aes-128-ecb", masterKey, Buffer.alloc(0));
const encryptedIV = ivCipher.update(iv, 'binary', 'hex') + ivCipher.final('hex');

console.log('🔐 تم تشفير المفاتيح بنجاح');

// حفظ الملف المشفر
fs.writeFileSync("index.encrypted", encrypted);

// إنشاء ملف C مع المفاتيح المشفرة
const cCode = `#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <emscripten.h>

// المفاتيح المشفرة كثوابت
const unsigned char encrypted_key[32] = {
    ${Buffer.from(encryptedKey, 'hex').toString('binary').split('').map(b => '0x' + b.charCodeAt(0).toString(16).padStart(2, '0')).join(', ')}
};

const unsigned char encrypted_iv[16] = {
    ${Buffer.from(encryptedIV, 'hex').toString('binary').split('').map(b => '0x' + b.charCodeAt(0).toString(16).padStart(2, '0')).join(', ')}
};

// المفتاح الرئيسي لفك تشفير المفاتيح
const unsigned char master_key[16] = {
    ${masterKey.toString('binary').split('').map(b => '0x' + b.charCodeAt(0).toString(16).padStart(2, '0')).join(', ')}
};

// دالة لفك تشفير المفاتيح
EMSCRIPTEN_KEEPALIVE
void decrypt_keys(unsigned char* output_key, unsigned char* output_iv) {
    // فك تشفير المفتاح باستخدام XOR مع المفتاح الرئيسي
    for (int i = 0; i < 32; i++) {
        output_key[i] = encrypted_key[i] ^ master_key[i % 16];
    }

    // فك تشفير IV باستخدام XOR مع المفتاح الرئيسي
    for (int i = 0; i < 16; i++) {
        output_iv[i] = encrypted_iv[i] ^ master_key[i % 16];
    }
}

// Simple XOR decryption for testing
EMSCRIPTEN_KEEPALIVE
char* simple_decrypt(const char* encrypted_data, const char* key) {
    if (!encrypted_data || !key) {
        return NULL;
    }

    int data_len = strlen(encrypted_data);
    int key_len = strlen(key);

    // Allocate memory for decrypted data
    char* decrypted = (char*)malloc(data_len + 1);
    if (!decrypted) {
        return NULL;
    }

    // Simple XOR decryption
    for (int i = 0; i < data_len; i++) {
        decrypted[i] = encrypted_data[i] ^ key[i % key_len];
    }
    decrypted[data_len] = '\0';

    return decrypted;
}

// Base64 decode function (مبسطة)
EMSCRIPTEN_KEEPALIVE
char* base64_decode(const char* encoded_data) {
    // For now, return as-is (سنضيف base64 decoding لاحقاً)
    int len = strlen(encoded_data);
    char* decoded = (char*)malloc(len + 1);
    strcpy(decoded, encoded_data);
    return decoded;
}

// Main decrypt function - معدلة لاستخدام المفاتيح من WASM
EMSCRIPTEN_KEEPALIVE
char* decrypt_data(const char* encrypted_base64) {
    printf("WASM: Starting decryption...\n");

    // فك تشفير المفاتيح أولاً
    unsigned char key[32];
    unsigned char iv[16];
    decrypt_keys(key, iv);

    // تحويل المفاتيح إلى نص hex للاستخدام في دالة فك التشفير الحالية
    char key_hex[65];
    char iv_hex[33];

    for (int i = 0; i < 32; i++) {
        sprintf(&key_hex[i*2], "%02x", key[i]);
    }
    key_hex[64] = '\0';

    for (int i = 0; i < 16; i++) {
        sprintf(&iv_hex[i*2], "%02x", iv[i]);
    }
    iv_hex[32] = '\0';

    // استخدام دالة فك التشفير الحالية
    char* decoded = base64_decode(encrypted_base64);
    char* result = simple_decrypt(decoded, key_hex);

    free(decoded);

    printf("WASM: Decryption completed\n");
    return result;
}

// دالة للحصول على المفتاح كنص (للاستخدام في JavaScript)
EMSCRIPTEN_KEEPALIVE
char* get_key_hex() {
    unsigned char key[32];
    decrypt_keys(key, NULL);

    char* key_hex = (char*)malloc(65);
    for (int i = 0; i < 32; i++) {
        sprintf(&key_hex[i*2], "%02x", key[i]);
    }
    key_hex[64] = '\0';

    return key_hex;
}

// دالة للحصول على IV كنص (للاستخدام في JavaScript)
EMSCRIPTEN_KEEPALIVE
char* get_iv_hex() {
    unsigned char iv[16];
    decrypt_keys(NULL, iv);

    char* iv_hex = (char*)malloc(33);
    for (int i = 0; i < 16; i++) {
        sprintf(&iv_hex[i*2], "%02x", iv[i]);
    }
    iv_hex[32] = '\0';

    return iv_hex;
}

// Free memory function
EMSCRIPTEN_KEEPALIVE
void free_memory(char* ptr) {
    if (ptr) {
        free(ptr);
    }
}
`;

fs.writeFileSync("secure-decrypt-fixed.c", cCode);

console.log('✅ تم حفظ الملفات:');
console.log('  - index.encrypted (الملف المشفر)');
console.log('  - secure-decrypt-fixed.c (ملف C مع المفاتيح المشفرة)');
console.log('');
console.log('🔥 الخطوات التالية:');
console.log('  1. قم ببناء WASM: build-secure-wasm.bat');
console.log('  2. ارفع index.encrypted على Cloudflare Workers');
console.log('  3. استخدم secure-loader.js لتشغيل البرنامج');
