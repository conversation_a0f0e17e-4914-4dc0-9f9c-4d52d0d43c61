#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <emscripten.h>

// المفاتيح المشفرة كثوابت
const unsigned char encrypted_key[32] = {
    0x0c, 0xff, 0xac, 0xe7, 0xf8, 0x91, 0xbe, 0x63, 0x75, 0xc2, 0x2a, 0x84, 0xd4, 0xe6, 0x0c, 0x4d, 0xdf, 0x0c, 0x63, 0xab, 0x51, 0x77, 0xcd, 0xf5, 0x58, 0x13, 0x44, 0xce, 0xa2, 0x2e, 0x98, 0xf5
};

const unsigned char encrypted_iv[16] = {
    0x91, 0x27, 0x43, 0x59, 0x96, 0xfc, 0x18, 0xba, 0x4b, 0xed, 0xa9, 0xa9, 0x3b, 0x21, 0x17, 0x87
};

// المفتاح الرئيسي لفك تشفير المفاتيح
const unsigned char master_key[16] = {
    0xe3, 0x6c, 0x04, 0x8d, 0x0a, 0xf3, 0x50, 0x51, 0xe4, 0x18, 0x61, 0x21, 0x62, 0x9d, 0x90, 0xd6
};

// دالة لفك تشفير المفاتيح
EMSCRIPTEN_KEEPALIVE
void decrypt_keys(unsigned char* output_key, unsigned char* output_iv) {
    // فك تشفير المفتاح باستخدام XOR مع المفتاح الرئيسي
    for (int i = 0; i < 32; i++) {
        output_key[i] = encrypted_key[i] ^ master_key[i % 16];
    }

    // فك تشفير IV باستخدام XOR مع المفتاح الرئيسي
    for (int i = 0; i < 16; i++) {
        output_iv[i] = encrypted_iv[i] ^ master_key[i % 16];
    }
}

// Simple XOR decryption for testing
EMSCRIPTEN_KEEPALIVE
char* simple_decrypt(const char* encrypted_data, const char* key) {
    if (!encrypted_data || !key) {
        return NULL;
    }

    int data_len = strlen(encrypted_data);
    int key_len = strlen(key);

    // Allocate memory for decrypted data
    char* decrypted = (char*)malloc(data_len + 1);
    if (!decrypted) {
        return NULL;
    }

    // Simple XOR decryption
    for (int i = 0; i < data_len; i++) {
        decrypted[i] = encrypted_data[i] ^ key[i % key_len];
    }
    decrypted[data_len] = '\0';

    return decrypted;
}

// Base64 decode function (مبسطة)
EMSCRIPTEN_KEEPALIVE
char* base64_decode(const char* encoded_data) {
    // For now, return as-is (سنضيف base64 decoding لاحقاً)
    int len = strlen(encoded_data);
    char* decoded = (char*)malloc(len + 1);
    strcpy(decoded, encoded_data);
    return decoded;
}

// Main decrypt function - معدلة لاستخدام المفاتيح من WASM
EMSCRIPTEN_KEEPALIVE
char* decrypt_data(const char* encrypted_base64) {
    printf("WASM: Starting decryption...\n");

    // فك تشفير المفاتيح أولاً
    unsigned char key[32];
    unsigned char iv[16];
    decrypt_keys(key, iv);

    // تحويل المفاتيح إلى نص hex للاستخدام في دالة فك التشفير الحالية
    char key_hex[65];
    char iv_hex[33];

    for (int i = 0; i < 32; i++) {
        sprintf(&key_hex[i*2], "%02x", key[i]);
    }
    key_hex[64] = '\0';

    for (int i = 0; i < 16; i++) {
        sprintf(&iv_hex[i*2], "%02x", iv[i]);
    }
    iv_hex[32] = '\0';

    // استخدام دالة فك التشفير الحالية
    char* decoded = base64_decode(encrypted_base64);
    char* result = simple_decrypt(decoded, key_hex);

    free(decoded);

    printf("WASM: Decryption completed\n");
    return result;
}

// دالة للحصول على المفتاح كنص (للاستخدام في JavaScript)
EMSCRIPTEN_KEEPALIVE
char* get_key_hex() {
    unsigned char key[32];
    decrypt_keys(key, NULL);

    char* key_hex = (char*)malloc(65);
    for (int i = 0; i < 32; i++) {
        sprintf(&key_hex[i*2], "%02x", key[i]);
    }
    key_hex[64] = '\0';

    return key_hex;
}

// دالة للحصول على IV كنص (للاستخدام في JavaScript)
EMSCRIPTEN_KEEPALIVE
char* get_iv_hex() {
    unsigned char iv[16];
    decrypt_keys(NULL, iv);

    char* iv_hex = (char*)malloc(33);
    for (int i = 0; i < 16; i++) {
        sprintf(&iv_hex[i*2], "%02x", iv[i]);
    }
    iv_hex[32] = '\0';

    return iv_hex;
}

// Free memory function
EMSCRIPTEN_KEEPALIVE
void free_memory(char* ptr) {
    if (ptr) {
        free(ptr);
    }
}
