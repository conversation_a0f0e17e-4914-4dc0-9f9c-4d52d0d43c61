# دليل رفع الملفات المشفرة إلى Cloudflare Workers

## الخطوة 1: تشفير الكود
إذا لم تقم بذلك بالفعل، قم بتشغيل:
```
node encrypt-secure.js
```
هذا سيقوم بإنشاء ملف `index.encrypted` و `secure-decrypt-fixed.c`.

## الخطوة 2: إنشاء Cloudflare Worker

1. اذهب إلى [Cloudflare Workers Dashboard](https://dash.cloudflare.com/workers)
2. انقر على "Create a Service"
3. أعطه اسماً مثل "encrypted-file-host"
4. انقر على "Create service"

## الخطوة 3: تحرير كود Worker

1. انقر على "Quick Edit"
2. استبدل الكود الافتراضي بهذا الكود:

```javascript
addEventListener('fetch', event => {
  event.respondWith(handleRequest(event.request))
})

async function handleRequest(request) {
  // قراءة الملف المشفر من متغيرات البيئة
  const encryptedData = ENCRYPTED_FILE

  // إرجاع الملف المشفر كرد
  return new Response(encryptedData, {
    headers: {
      'Content-Type': 'text/plain',
      'Access-Control-Allow-Origin': '*'
    }
  })
}
```

3. انقر على "Save and Deploy"

## الخطوة 4: إضافة متغير البيئة

1. بعد حفظ الكود، انقر على "Settings"
2. انقر على "Variables"
3. تحت "Environment Variables"، انقر على "Add variable"
4. اسم المتغير: `ENCRYPTED_FILE`
5. القيمة: انسخ محتوى ملف `index.encrypted` الذي تم إنشاؤه
6. انقر على "Save"

## الخطوة 5: تحديث الرابط في secure-loader.js

1. افتح ملف `secure-loader.js`
2. ابحث عن هذا السطر:
   ```javascript
   const url = 'https://your-worker.your-subdomain.workers.dev/index.encrypted';
   ```
3. استبدله بالرابط الذي نسخته من Cloudflare Workers

## الخطوة 6: تشغيل البرنامج

الآن يمكنك تشغيل البرنامج:
```
node secure-loader.js
```

## ملاحظات

- تأكد من أن ملف `index.encrypted` موجود في نفس المجلد قبل تشغيل `node encrypt-secure.js`
- تأكد من تحديث الرابط في `secure-loader.js` بشكل صحيح
- إذا واجهت أي مشاكل، تحقق من أن Worker تم نشره بشكل صحيح وأن متغير البيئة تم تعيينه بشكل صحيح
