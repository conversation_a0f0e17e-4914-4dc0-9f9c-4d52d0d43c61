// تحميل متغيرات البيئة
require('dotenv').config();

// المفاتيح الحساسة - مدمجة في الكود
const PAYPAL_LIVE_CLIENT_ID = 'AZcJ950YgmuYB21HQuvNsgf51bwwiKJ3RNYxyn52jJa1wNAY3Po8ddQPof9v2ZUlEwPQ23ytd1TITj3f';
const PAYPAL_LIVE_CLIENT_SECRET = 'EGKjm9ePunGucJLWGMhpAl4-vNSVCh_fAFs_6ALxca2bdkgaQSAxKMKNC0wtWLZaleq7c0h9T2-k2A-6';
const PAYPAL_PLAN_ID = 'P-24S82074UR896690XNA7KXRA';

// Firebase configuration - مدمجة في الكود
const FIREBASE_CONFIG = {
  apiKey: "AIzaSyDAG1ZwOOND0gyZSRcbUF1Qoh2SYN9R4Hs",
  authDomain: "streamtok-c6830.firebaseapp.com",
  projectId: "streamtok-c6830",
  storageBucket: "streamtok-c6830.firebasestorage.app",
  messagingSenderId: "123501258730",
  appId: "1:123501258730:web:690222fb0e45b217fbb493"
};

const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const { Server } = require('socket.io');
const { WebcastPushConnection } = require('tiktok-live-connector');
const { fetchGiftsList } = require('./gift-service');
const fs = require('fs-extra');
const path = require('path');
const robot = require('robotjs');
const axios = require('axios');
const { exec } = require('child_process');
const { v4: uuidv4 } = require('uuid');
const os = require('os');
const { EdgeTTS } = require('node-edge-tts');
const multer = require('multer');

// Initialize robotjs for keyboard simulation
let robotInitialized = false;
try {
  // استخدام الإعدادات الافتراضية مثل KeyboardSimulator.js تماماً
  robotInitialized = true;
  console.log('RobotJS initialized successfully for keyboard simulation (using default settings)');
} catch (err) {
  console.error('Failed to initialize RobotJS:', err);
}

// Helper function to map keys for keypress simulation
function mapKeyForSim(key, modifiers = {}) {
  if (!robotInitialized) {
    console.error('RobotJS not initialized');
    return null;
  }

  // Lowercase the key for consistency
  const lowerKey = key.toLowerCase().trim();

  // Map for special keys
  const keyMap = {
    'space': 'space',
    'spacebar': 'space',
    ' ': 'space',
    'enter': 'enter',
    'return': 'enter',
    'tab': 'tab',
    'escape': 'escape',
    'esc': 'escape',
    'up': 'up',
    'down': 'down',
    'left': 'left',
    'right': 'right',
    'arrowup': 'up',
    'arrowdown': 'down',
    'arrowleft': 'left',
    'arrowright': 'right',
    'backspace': 'backspace',
    'delete': 'delete',
    'control': 'control',
    'ctrl': 'control',
    'alt': 'alt',
    'shift': 'shift',
    'caps': 'capslock',
    'capslock': 'capslock'
  };

  // Get the mapped key
  const mappedKey = keyMap[lowerKey] || lowerKey;

  try {
    // Handle modifiers - استخدام نفس طريقة KeyboardSimulator.js الناجحة
    const modifierKeys = [];
    if (modifiers && typeof modifiers === 'object') {
      if (modifiers.ctrl) modifierKeys.push('control');
      if (modifiers.alt) modifierKeys.push('alt');
      if (modifiers.shift) modifierKeys.push('shift');
    }

    // استخدام robot.keyTap مثل KeyboardSimulator.js تماماً
    if (modifierKeys.length > 0) {
      robot.keyTap(mappedKey, modifierKeys);
      console.log(`✅ Pressed key: ${mappedKey} with modifiers: ${modifierKeys.join('+')}`);
    } else {
      robot.keyTap(mappedKey);
      console.log(`✅ Pressed key: ${mappedKey}`);
    }

    return mappedKey;
  } catch (err) {
    console.error('Failed to simulate key with RobotJS:', err);
    return null;
  }
}

// Helper function for RobotJS fallback
function mapKeyForRobot(key) {
  // Lowercase the key for consistency
  const lowerKey = key.toLowerCase().trim();

  // Map for special keys
  const keyMap = {
    'space': 'space',
    'spacebar': 'space',
    ' ': 'space',
    'enter': 'enter',
    'return': 'enter',
    'tab': 'tab',
    'escape': 'escape',
    'esc': 'escape',
    'up': 'up',
    'down': 'down',
    'left': 'left',
    'right': 'right',
    'arrowup': 'up',
    'arrowdown': 'down',
    'arrowleft': 'left',
    'arrowright': 'right',
    'backspace': 'backspace',
    'delete': 'delete',
    'control': 'control',
    'ctrl': 'control',
    'alt': 'alt',
    'shift': 'shift',
    'caps': 'capslock',
    'capslock': 'capslock'
  };

  // Return mapped key or original key if no mapping exists
  return keyMap[lowerKey] || lowerKey;
}

// حفظ اسم المستخدم الأخير في ملف منفصل
function saveLastUsername(username) {
  try {
    const filePath = path.join(__dirname, 'last_username.json');
    fs.writeFileSync(filePath, JSON.stringify({ username }), 'utf8');
  } catch (err) {
    console.error('Error saving last username:', err);
  }
}

// استرجاع اسم المستخدم الأخير من الملف
function loadLastUsername() {
  try {
    const filePath = path.join(__dirname, 'last_username.json');
    if (fs.existsSync(filePath)) {
      const data = JSON.parse(fs.readFileSync(filePath, 'utf8'));
      return data.username || '';
    }
  } catch (err) {
    console.error('Error loading last username:', err);
  }
  return '';
}

const app = express();
const server = http.createServer(app);
const io = new Server(server, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"]
  },
  maxHttpBufferSize: 50 * 1024 * 1024,  // 50 MB - لدعم الملفات الكبيرة
  pingTimeout: 60000,
  pingInterval: 25000,
  // تصحيح مشكلة الاتصال
  allowEIO3: true, // السماح بالإصدار 3 من بروتوكول Socket.IO
  transports: ['polling', 'websocket'], // السماح بنقل البيانات عبر polling و websocket
  path: '/socket.io/' // التأكد من أن المسار صحيح
});

// تسجيل أخطاء Socket.IO
io.engine.on("connection_error", (err) => {
  console.log("Socket.IO connection error:", err.req);      // the request object
  console.log("Socket.IO connection error code:", err.code);     // the error code, for example 1
  console.log("Socket.IO connection error message:", err.message);  // the error message, for example "Session ID unknown"
  console.log("Socket.IO connection error context:", err.context);  // some additional error context
});

// إنشاء مجلد للملفات المحملة إذا لم يكن موجوداً
const uploadsDir = path.join(__dirname, 'public', 'uploads');
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
}

// إعداد multer لتحميل الملفات
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, uploadsDir);
  },
  filename: function (req, file, cb) {
    // إنشاء اسم فريد للملف مع الاحتفاظ بامتداده الأصلي
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const fileExt = path.extname(file.originalname);
    cb(null, uniqueSuffix + fileExt);
  }
});

// تحديد حجم الملف الأقصى (200 ميجابايت)
const upload = multer({
  storage: storage,
  limits: { fileSize: 200 * 1024 * 1024 }
});

// Serve static files from the public directory
app.use(express.static('public'));

// خدمة الملفات المحملة
app.use('/uploads', express.static(path.join(__dirname, 'public', 'uploads')));

// Store active TikTok connection
let tiktokConnection = null;
let activeUsername = '';

// Store gift mappings
let giftMappings = [];

// نظام حماية الاشتراكات من الخادم
let userSessions = new Map(); // تخزين sessions المستخدمين
let subscriptionCache = new Map(); // تخزين مؤقت للاشتراكات (5 دقائق)
const CACHE_DURATION = 5 * 60 * 1000; // 5 دقائق

// cache بسيط لحالة الاشتراك
let subscriptionStatus = {
  hasSubscription: false,
  lastChecked: 0,
  checkInterval: 5 * 60 * 60 * 1000, // 5 ساعات
  currentUserId: null // المستخدم الحالي
};

// نظام مراقبة الاشتراكات المنتهية
let subscriptionMonitor = null;
const MONITOR_INTERVAL = 60 * 60 * 1000; // فحص كل ساعة (3600 ثانية)

// فحص الاشتراك مع cache ذكي
async function checkSubscriptionWithCache(userId) {
  const now = Date.now();

  // فحص كل 5 ساعات أو في المرة الأولى
  if (subscriptionStatus.lastChecked === 0 ||
    (now - subscriptionStatus.lastChecked) > subscriptionStatus.checkInterval) {

    console.log('🔍 فحص الاشتراك من Firebase...');
    subscriptionStatus.hasSubscription = await checkSubscriptionFromServer(userId);
    subscriptionStatus.lastChecked = now;
  }

  return subscriptionStatus.hasSubscription;
}

// فحص دوري كل 5 ساعات
setInterval(async () => {
  if (subscriptionStatus.currentUserId) {
    console.log('🔄 فحص دوري للاشتراك (كل 5 ساعات)...');
    subscriptionStatus.hasSubscription = await checkSubscriptionFromServer(subscriptionStatus.currentUserId);
    subscriptionStatus.lastChecked = Date.now();
    console.log(`✅ حالة الاشتراك المحدثة: ${subscriptionStatus.hasSubscription ? 'نشط' : 'غير نشط'}`);
  }
}, subscriptionStatus.checkInterval);

// دالة التحقق من الاشتراك من Firebase (تحقق حقيقي)
async function checkSubscriptionFromServer(userId) {
  try {
    if (!userId) {
      console.log('❌ No userId provided');
      return false;
    }

    console.log(`🔍 Server checking subscription for: ${userId}`);

    // استخدام Firebase REST API للتحقق من الاشتراك
    const firebaseProjectId = 'streamtok-c6830';
    const firebaseApiKey = 'AIzaSyDAG1ZwOOND0gyZSRcbUF1Qoh2SYN9R4Hs';
    const baseUrl = `https://firestore.googleapis.com/v1/projects/${firebaseProjectId}/databases/(default)/documents`;

    console.log(`📡 Checking subscriptions for user: ${userId}`);

    try {
      // البحث في collection subscriptions
      const subscriptionsUrl = `${baseUrl}/subscriptions`;

      // استعلام للبحث عن اشتراكات المستخدم مع API key
      const response = await axios.get(subscriptionsUrl, {
        params: {
          key: firebaseApiKey
        }
      });

      if (response.data && response.data.documents) {
        console.log(`📄 Found ${response.data.documents.length} subscription documents`);

        // البحث عن اشتراك نشط للمستخدم
        let activeSubscription = null;
        let latestDate = null;
        let userSubscriptions = 0;

        for (const doc of response.data.documents) {
          const data = doc.fields;

          // التحقق من userId
          if (data.userId && data.userId.stringValue === userId) {
            userSubscriptions++;
            console.log(`🔍 Found subscription for user ${userId}:`);
            console.log(`   - Status: ${data.status ? data.status.stringValue : 'undefined'}`);
            console.log(`   - EndDate: ${data.endDate ? data.endDate.timestampValue : 'undefined'}`);

            // التحقق من الحالة
            if (data.status && data.status.stringValue === 'active') {
              // التحقق من تاريخ الانتهاء
              const endDate = data.endDate ? new Date(data.endDate.timestampValue) : null;
              const now = new Date();

              console.log(`   - EndDate parsed: ${endDate}`);
              console.log(`   - Current time: ${now}`);

              const isValid = endDate && now < endDate;
              console.log(`   - Is valid: ${isValid}`);

              if (isValid) {
                const createdAt = data.createdAt ? new Date(data.createdAt.timestampValue) : new Date();
                if (!latestDate || createdAt > latestDate) {
                  latestDate = createdAt;
                  activeSubscription = data;
                }
              } else {
                console.log(`   - ⚠️ Subscription expired or no end date`);
              }
            } else {
              console.log(`   - ⚠️ Subscription not active`);
            }
          }
        }

        console.log(`📊 User ${userId} has ${userSubscriptions} subscription(s) in database`);

        if (activeSubscription) {
          console.log(`✅ Active subscription found for user: ${userId}`);
          return true;
        } else {
          console.log(`❌ No active subscription found for user: ${userId}`);
          return false;
        }
      } else {
        console.log(`❌ No subscriptions collection found or empty response`);
        console.log(`📄 Response data:`, response.data);
        return false;
      }

    } catch (apiError) {
      console.log('⚠️ Firebase API call failed:', apiError.message);

      // تحقق من نوع الخطأ
      if (apiError.response && apiError.response.status === 403) {
        console.log('🔒 Access denied - check Firestore Rules');
      } else if (apiError.response && apiError.response.status === 404) {
        console.log('📭 No subscriptions collection found');
      }

      // في حالة فشل API، نرجع false للأمان (fail-safe)
      console.log(`❌ API failed - denying access for user: ${userId}`);
      return false;
    }

  } catch (error) {
    console.error('Error checking subscription from server:', error);
    return false;
  }
}

// دالة للحصول على userId من socket (من session أو authentication)
function getUserIdFromSocket(socket) {
  const userSession = userSessions.get(socket.id);
  console.log(`🔍 Getting userId for socket ${socket.id}:`, userSession ? `Found user: ${userSession.email}` : 'No session found');
  console.log(`📊 Available sessions: ${Array.from(userSessions.keys()).join(', ')}`);
  return userSession ? userSession.userId : null;
}

// دالة مراقبة الاشتراكات المنتهية (فقط تحديث cache الخادم)
async function monitorExpiredSubscriptions() {
  try {
    const startTime = new Date();
    console.log(`🔍 [${startTime.toLocaleTimeString()}] Starting hourly subscription monitoring...`);

    let expiredCount = 0;
    let activeCount = 0;
    let totalChecked = 0;

    // فحص جميع المستخدمين المتصلين وتحديث cache الخادم
    for (const [socketId, session] of userSessions.entries()) {
      if (session.userId) {
        totalChecked++;
        const hasSubscription = await checkSubscriptionFromServer(session.userId);

        // تحديث cache الخادم فقط
        if (!hasSubscription && session.hasActiveSubscription !== false) {
          session.hasActiveSubscription = false;
          expiredCount++;
          console.log(`⚠️ User subscription expired: ${session.email}`);
        }
        else if (hasSubscription) {
          session.hasActiveSubscription = true;
          activeCount++;
        }
      }
    }

    const endTime = new Date();
    const duration = endTime - startTime;

    console.log(`📊 Hourly monitoring completed in ${duration}ms:`);
    console.log(`   - Total users checked: ${totalChecked}`);
    console.log(`   - Active subscriptions: ${activeCount}`);
    console.log(`   - Expired subscriptions: ${expiredCount}`);
    console.log(`   - Next check in 1 hour at: ${new Date(Date.now() + MONITOR_INTERVAL).toLocaleTimeString()}`);

  } catch (error) {
    console.error('Error monitoring subscriptions:', error);
  }
}

// بدء مراقبة الاشتراكات
function startSubscriptionMonitoring() {
  if (subscriptionMonitor) {
    clearInterval(subscriptionMonitor);
  }

  subscriptionMonitor = setInterval(monitorExpiredSubscriptions, MONITOR_INTERVAL);
  console.log('🔄 Subscription monitoring started (checking every hour)');
}

// متغير لتخزين إجمالي عدد الإعجابات لكل مستخدم
const userLikeCounts = {};

// Store TikTok gift data
let tikTokGifts = [];

// استيراد نماذج TTS من ملف tts-voices.js
const { ttsModels } = require('./public/tts-voices.js');

// إعدادات قراءة النص إلى كلام
let ttsSettings = {
  enabled: true,
  voice: "ar-EG-SalmaNeural", // النموذج الافتراضي هو صوت سلمى باللغة العربية
  speed: 1.0,
  volume: 50, // مستوى الصوت (من 1 إلى 100)
  minLength: 5,
  maxLength: 500,
  readRate: 2, // قراءة تعليق واحد من كل X تعليقات
  blockedWords: [],
  models: ttsModels // استخدام النماذج من ملف tts-voices.js
};

// Store display settings
let displaySettings = {
  showUsernames: true,
  showGiftNames: true,
  showGiftImages: true,
  showDiamondCount: true,
  alertPosition: 'bottom-left',
  alertTheme: 'default',
  alertSize: 'normal',
  alertDuration: 5,
  showBigGifts: true,
  showFollows: false,
  showLikes: false,
  showComments: false,
  backgroundColor: '#000000',
  textColor: '#ffffff',
  accentColor: '#ff3b5c',
  fontFamily: "'Tajawal', sans-serif",
  fontSize: 16,
  enableAnimations: true,
  animationType: 'random',
  width: 30,
  opacity: 80,
  backgroundOpacity: 80,
  maxItems: 5,
  customPosition: { x: 10, y: 10 },
  sound: { enabled: false, volume: 50 }
};

// Store profiles
let profiles = [];
let activeProfile = 'default';

// Profiles directory
const profilesDir = path.join(__dirname, 'profiles');
if (!fs.existsSync(profilesDir)) {
  fs.mkdirSync(profilesDir, { recursive: true });
}

// Load profiles
function loadProfiles() {
  try {
    // Check if profiles list file exists
    const profilesListPath = path.join(profilesDir, 'profiles_list.json');
    if (fs.existsSync(profilesListPath)) {
      profiles = JSON.parse(fs.readFileSync(profilesListPath, 'utf8'));
    } else {
      // Create default profile
      profiles = [{
        id: 'default',
        name: 'الإعدادات الافتراضية',
        created: Date.now(),
        lastModified: Date.now()
      }];
      fs.writeFileSync(profilesListPath, JSON.stringify(profiles, null, 2), 'utf8');
    }

    // Load active profile
    const activeProfilePath = path.join(profilesDir, 'active_profile.json');
    if (fs.existsSync(activeProfilePath)) {
      activeProfile = fs.readFileSync(activeProfilePath, 'utf8');
    } else {
      fs.writeFileSync(activeProfilePath, 'default', 'utf8');
    }

    // Load active profile data
    loadProfileData(activeProfile);

    console.log(`Loaded ${profiles.length} profiles. Active profile: ${activeProfile}`);
  } catch (error) {
    console.error('Error loading profiles:', error);
  }
}

// Save profiles list
function saveProfilesList() {
  const profilesListPath = path.join(profilesDir, 'profiles_list.json');
  fs.writeFileSync(profilesListPath, JSON.stringify(profiles, null, 2), 'utf8');
}

// Load specific profile data
function loadProfileData(profileId) {
  try {
    const profileDataPath = path.join(profilesDir, `${profileId}.json`);

    // If profile data doesn't exist, create it with current settings
    if (!fs.existsSync(profileDataPath)) {
      saveProfileData(profileId);
      return;
    }

    const profileData = JSON.parse(fs.readFileSync(profileDataPath, 'utf8'));

    // Update settings from profile
    if (profileData.giftMappings) {
      giftMappings = JSON.parse(JSON.stringify(profileData.giftMappings));
    }

    if (profileData.displaySettings) {
      // Crear una copia profunda de las configuraciones predeterminadas
      const defaultSettings = JSON.parse(JSON.stringify(displaySettings));

      // Combinar con las configuraciones del perfil
      const loadedSettings = profileData.displaySettings;

      // Propiedades básicas
      defaultSettings.showUsernames = loadedSettings.showUsernames !== undefined ? loadedSettings.showUsernames : defaultSettings.showUsernames;
      defaultSettings.showGiftNames = loadedSettings.showGiftNames !== undefined ? loadedSettings.showGiftNames : defaultSettings.showGiftNames;
      defaultSettings.showGiftImages = loadedSettings.showGiftImages !== undefined ? loadedSettings.showGiftImages : defaultSettings.showGiftImages;
      defaultSettings.showDiamondCount = loadedSettings.showDiamondCount !== undefined ? loadedSettings.showDiamondCount : defaultSettings.showDiamondCount;
      defaultSettings.alertPosition = loadedSettings.alertPosition || defaultSettings.alertPosition;
      defaultSettings.alertTheme = loadedSettings.alertTheme || defaultSettings.alertTheme;
      defaultSettings.alertSize = loadedSettings.alertSize || defaultSettings.alertSize;
      defaultSettings.alertDuration = loadedSettings.alertDuration !== undefined ? loadedSettings.alertDuration : defaultSettings.alertDuration;
      defaultSettings.showBigGifts = loadedSettings.showBigGifts !== undefined ? loadedSettings.showBigGifts : defaultSettings.showBigGifts;
      defaultSettings.showFollows = loadedSettings.showFollows !== undefined ? loadedSettings.showFollows : defaultSettings.showFollows;
      defaultSettings.showLikes = loadedSettings.showLikes !== undefined ? loadedSettings.showLikes : defaultSettings.showLikes;
      defaultSettings.showComments = loadedSettings.showComments !== undefined ? loadedSettings.showComments : defaultSettings.showComments;

      // Propiedades de estilo
      defaultSettings.backgroundColor = loadedSettings.backgroundColor || defaultSettings.backgroundColor;
      defaultSettings.textColor = loadedSettings.textColor || defaultSettings.textColor;
      defaultSettings.accentColor = loadedSettings.accentColor || defaultSettings.accentColor;
      defaultSettings.fontFamily = loadedSettings.fontFamily || defaultSettings.fontFamily;

      // Propiedades numéricas (asegurarse de que sean números)
      defaultSettings.fontSize = typeof loadedSettings.fontSize === 'number' ? loadedSettings.fontSize :
        (loadedSettings.fontSize ? parseInt(loadedSettings.fontSize, 10) : defaultSettings.fontSize);

      defaultSettings.width = typeof loadedSettings.width === 'number' ? loadedSettings.width :
        (loadedSettings.width ? parseInt(loadedSettings.width, 10) : defaultSettings.width);

      defaultSettings.opacity = typeof loadedSettings.opacity === 'number' ? loadedSettings.opacity :
        (loadedSettings.opacity ? parseInt(loadedSettings.opacity, 10) : defaultSettings.opacity);

      defaultSettings.backgroundOpacity = typeof loadedSettings.backgroundOpacity === 'number' ? loadedSettings.backgroundOpacity :
        (loadedSettings.backgroundOpacity ? parseInt(loadedSettings.backgroundOpacity, 10) : defaultSettings.backgroundOpacity);

      defaultSettings.maxItems = typeof loadedSettings.maxItems === 'number' ? loadedSettings.maxItems :
        (loadedSettings.maxItems ? parseInt(loadedSettings.maxItems, 10) : defaultSettings.maxItems);

      // Propiedades booleanas
      defaultSettings.enableAnimations = loadedSettings.enableAnimations !== undefined ? loadedSettings.enableAnimations : defaultSettings.enableAnimations;

      // Propiedades de texto
      defaultSettings.animationType = loadedSettings.animationType || defaultSettings.animationType;

      // Objetos anidados
      if (loadedSettings.customPosition) {
        defaultSettings.customPosition = {
          x: typeof loadedSettings.customPosition.x === 'number' ? loadedSettings.customPosition.x :
            (loadedSettings.customPosition.x ? parseInt(loadedSettings.customPosition.x, 10) : defaultSettings.customPosition.x),
          y: typeof loadedSettings.customPosition.y === 'number' ? loadedSettings.customPosition.y :
            (loadedSettings.customPosition.y ? parseInt(loadedSettings.customPosition.y, 10) : defaultSettings.customPosition.y)
        };
      }

      if (loadedSettings.sound) {
        defaultSettings.sound = {
          enabled: loadedSettings.sound.enabled !== undefined ? loadedSettings.sound.enabled : defaultSettings.sound.enabled,
          volume: typeof loadedSettings.sound.volume === 'number' ? loadedSettings.sound.volume :
            (loadedSettings.sound.volume ? parseInt(loadedSettings.sound.volume, 10) : defaultSettings.sound.volume)
        };
      }

      // Actualizar las configuraciones globales
      displaySettings = defaultSettings;

      console.log('Configuraciones cargadas con éxito:', displaySettings);
    }

    // تحميل إعدادات TTS من ملف البروفايل
    if (profileData.ttsSettings) {
      // إنشاء نسخة من الإعدادات الافتراضية
      const defaultTtsSettings = JSON.parse(JSON.stringify(ttsSettings));

      // دمج مع الإعدادات المحملة من الملف
      const loadedTtsSettings = profileData.ttsSettings;

      // تحديث الإعدادات
      ttsSettings = { ...defaultTtsSettings, ...loadedTtsSettings };

      // تحديث إعدادات TTS في ملف speakText-edge-tts.js
      const { updateTtsSettings } = require('./speakText-edge-tts.js');
      updateTtsSettings(ttsSettings);

      console.log('تم تحميل إعدادات TTS بنجاح:', ttsSettings);
    }

    console.log(`Loaded profile data for "${profileId}"`);
  } catch (error) {
    console.error(`Error loading profile data for "${profileId}":`, error);
  }
}

// Save current settings to profile
function saveProfileData(profileId) {
  try {
    const profileData = {
      giftMappings: giftMappings,
      displaySettings: displaySettings,
      ttsSettings: ttsSettings // إضافة إعدادات TTS للملف الشخصي
    };

    const profileDataPath = path.join(profilesDir, `${profileId}.json`);
    fs.writeFileSync(profileDataPath, JSON.stringify(profileData, null, 2), 'utf8');

    // Update last modified date
    const profileIndex = profiles.findIndex(p => p.id === profileId);
    if (profileIndex !== -1) {
      profiles[profileIndex].lastModified = Date.now();
      saveProfilesList();
    }

    console.log(`Saved profile data for "${profileId}"`);
  } catch (error) {
    console.error(`Error saving profile data for "${profileId}":`, error);
  }
}

// Set active profile
function setActiveProfile(profileId) {
  try {
    // Save current profile data before switching
    saveProfileData(activeProfile);

    // Update active profile
    activeProfile = profileId;
    const activeProfilePath = path.join(profilesDir, 'active_profile.json');
    fs.writeFileSync(activeProfilePath, activeProfile, 'utf8');

    // Load new profile data
    loadProfileData(profileId);

    console.log(`Switched to profile: ${profileId}`);
    return true;
  } catch (error) {
    console.error(`Error setting active profile "${profileId}":`, error);
    return false;
  }
}

// Initialize profiles
loadProfiles();

// Fetch gift data when server starts
async function initializeGiftData() {
  try {
    console.log('Fetching TikTok gift data...');
    tikTokGifts = await fetchGiftsList();
    console.log(`Successfully loaded ${tikTokGifts.length} gifts from TikTok API`);
  } catch (error) {
    console.error('Failed to fetch gift data:', error.message);
    console.log('Will retry in 5 minutes');
    // Retry after 5 minutes
    setTimeout(initializeGiftData, 5 * 60 * 1000);
  }
}

// Initialize gift data
initializeGiftData();

// Add an API endpoint to get the gifts data
app.get('/api/gifts', (req, res) => {
  res.json(tikTokGifts);
});

// API endpoint for Firebase configuration
app.get('/api/firebase-config', (req, res) => {
  res.json(FIREBASE_CONFIG);
});

// Add an API endpoint for profiles
app.get('/api/profiles', (req, res) => {
  res.json({
    profiles: profiles,
    activeProfile: activeProfile
  });
});

// Routes - تعديل المسارات لتوجيه إلى الصفحات المنفصلة
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'connection.html'));
});

// حماية صفحة الألعاب من الخادم
app.get('/games.html', async (req, res) => {
  // للآن، نسمح بالوصول لجميع المستخدمين
  // في التطبيق الحقيقي، سيتم التحقق من session أو token
  console.log('🎮 Games page accessed');
  res.sendFile(path.join(__dirname, 'public', 'games.html'));
});

// مسار صفحة ربط الهدايا
app.get('/mappings.html', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'mappings.html'));
});

// مسار صفحة الإعدادات
app.get('/settings.html', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'settings.html'));
});

// مسار صفحة الاتصال
app.get('/contact.html', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'contact.html'));
});

// مسار صفحة العرض (Overlay)
app.get('/overlay.html', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'overlay.html'));
});

// مسار إضافي لصفحة العرض
app.get('/overlay', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'overlay.html'));
});

// مسار صفحة قراءة التعليقات الصوتية
app.get('/tts-comments.html', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'tts-comments.html'));
});

// مسار صفحة الملفات الشخصية
app.get('/profiles.html', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'profiles.html'));
});

// مسارات الألعاب الفردية
app.get('/games/:gameName', (req, res) => {
  const gameName = req.params.gameName;
  const gamePath = path.join(__dirname, 'public', 'games', gameName);
  res.sendFile(gamePath);
});

// مسار لعبة Fish Eat Fish
app.get('/games/fish-eat-fish.html', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'games', 'fish-eat-fish.html'));
});



// مسار تحميل الملفات
app.post('/upload-media', upload.single('file'), (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ success: false, error: 'لم يتم تحميل أي ملف' });
    }

    // إرجاع مسار الملف المحمل
    const filePath = '/uploads/' + req.file.filename;
    return res.json({
      success: true,
      filePath: filePath,
      fileName: req.file.originalname
    });
  } catch (error) {
    console.error('خطأ في تحميل الملف:', error);
    return res.status(500).json({ success: false, error: error.message });
  }
});

// ===========================
// PayPal Integration
// ===========================

// PayPal configuration
const PAYPAL_CONFIG = {
  mode: process.env.PAYPAL_MODE || 'sandbox',
  client_id: process.env.PAYPAL_MODE === 'live'
    ? PAYPAL_LIVE_CLIENT_ID
    : process.env.PAYPAL_SANDBOX_CLIENT_ID,
  client_secret: process.env.PAYPAL_MODE === 'live'
    ? PAYPAL_LIVE_CLIENT_SECRET
    : process.env.PAYPAL_SANDBOX_CLIENT_SECRET,
  plan_id: PAYPAL_PLAN_ID
};

// PayPal API base URL
const PAYPAL_API_BASE = PAYPAL_CONFIG.mode === 'live'
  ? 'https://api.paypal.com'
  : 'https://api.sandbox.paypal.com';

// Get PayPal access token
async function getPayPalAccessToken() {
  try {
    const auth = Buffer.from(`${PAYPAL_CONFIG.client_id}:${PAYPAL_CONFIG.client_secret}`).toString('base64');

    const response = await axios.post(`${PAYPAL_API_BASE}/v1/oauth2/token`,
      'grant_type=client_credentials',
      {
        headers: {
          'Authorization': `Basic ${auth}`,
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      }
    );

    return response.data.access_token;
  } catch (error) {
    console.error('Error getting PayPal access token:', error.response?.data || error.message);
    throw error;
  }
}

// Get PayPal configuration for frontend
app.get('/api/paypal/config', (req, res) => {
  try {
    res.json({
      success: true,
      client_id: PAYPAL_CONFIG.client_id,
      plan_id: PAYPAL_CONFIG.plan_id,
      mode: PAYPAL_CONFIG.mode
    });
  } catch (error) {
    console.error('Error getting PayPal config:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get PayPal configuration'
    });
  }
});

// Verify PayPal subscription
app.post('/api/paypal/verify-subscription', async (req, res) => {
  try {
    const { subscriptionId, userId } = req.body;

    if (!subscriptionId || !userId) {
      return res.status(400).json({
        success: false,
        error: 'Missing subscription ID or user ID'
      });
    }

    // Get PayPal access token
    const accessToken = await getPayPalAccessToken();

    // Get subscription details from PayPal
    const response = await axios.get(`${PAYPAL_API_BASE}/v1/billing/subscriptions/${subscriptionId}`, {
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json'
      }
    });

    const subscription = response.data;
    console.log('PayPal subscription details:', subscription);

    // Check if subscription is active
    if (subscription.status === 'ACTIVE') {
      // Save subscription to Firebase (this will be handled by frontend)
      res.json({
        success: true,
        subscription: {
          id: subscriptionId,
          status: subscription.status,
          plan_id: subscription.plan_id,
          start_time: subscription.start_time,
          billing_info: subscription.billing_info
        }
      });
    } else {
      res.json({
        success: false,
        error: 'Subscription is not active',
        status: subscription.status
      });
    }

  } catch (error) {
    console.error('Error verifying PayPal subscription:', error.response?.data || error.message);
    res.status(500).json({
      success: false,
      error: 'Failed to verify subscription'
    });
  }
});

// Cancel PayPal subscription
app.post('/api/paypal/cancel-subscription', async (req, res) => {
  try {
    const { subscriptionId, reason } = req.body;

    if (!subscriptionId) {
      return res.status(400).json({
        success: false,
        error: 'Missing subscription ID'
      });
    }

    // Get PayPal access token
    const accessToken = await getPayPalAccessToken();

    // Cancel subscription
    const response = await axios.post(
      `${PAYPAL_API_BASE}/v1/billing/subscriptions/${subscriptionId}/cancel`,
      {
        reason: reason || 'User requested cancellation'
      },
      {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        }
      }
    );

    res.json({
      success: true,
      message: 'Subscription cancelled successfully'
    });

  } catch (error) {
    console.error('Error cancelling PayPal subscription:', error.response?.data || error.message);
    res.status(500).json({
      success: false,
      error: 'Failed to cancel subscription'
    });
  }
});

// ===========================
// دالة إرسال الأحداث للغرف المناسبة
// ===========================

function sendEventToAppropriateOverlays(eventType, eventData) {
  try {
    // جمع جميع overlay IDs التي لها تعيينات لهذا النوع من الأحداث
    const targetOverlays = new Set();

    console.log(`🔍 البحث عن تعيينات للحدث ${eventType}...`);

    if (giftMappings && Array.isArray(giftMappings)) {
      giftMappings.forEach(mapping => {
        let shouldInclude = false;

        // للهدايا: التحقق من تطابق الهدية أو نوع الحدث
        if (eventType === 'gift' || eventType === 'simulatedGift') {
          if (eventData.giftId && String(mapping.giftId) === String(eventData.giftId)) {
            shouldInclude = true;
            console.log(`✅ تطابق هدية بالمعرف: ${eventData.giftId} → overlay ${mapping.overlayId || 'default'}`);
          } else if (eventData.giftName && mapping.giftName &&
            mapping.giftName.trim().toLowerCase() === eventData.giftName.trim().toLowerCase()) {
            shouldInclude = true;
            console.log(`✅ تطابق هدية بالاسم: ${eventData.giftName} → overlay ${mapping.overlayId || 'default'}`);
          } else if (mapping.giftId === 'any') {
            shouldInclude = true;
            console.log(`✅ تطابق هدية عامة (any) → overlay ${mapping.overlayId || 'default'}`);
          }
        }
        // للأحداث الأخرى: التحقق من نوع الحدث
        else if (mapping.eventType === eventType.replace('simulated', '').toLowerCase()) {
          shouldInclude = true;
          console.log(`✅ تطابق نوع حدث: ${mapping.eventType} → overlay ${mapping.overlayId || 'default'}`);
        }

        if (shouldInclude) {
          targetOverlays.add(mapping.overlayId || 'default');
        }
      });
    }

    console.log(`🎯 الغرف المستهدفة:`, Array.from(targetOverlays));

    // إذا لم توجد تعيينات محددة، إرسال لجميع الغرف
    if (targetOverlays.size === 0) {
      console.log(`⚠️ لا توجد تعيينات محددة للحدث ${eventType}، إرسال لجميع الغرف`);
      io.emit(eventType, eventData);
      return;
    }

    // إرسال للغرف المحددة
    targetOverlays.forEach(overlayId => {
      const roomName = `overlay-${overlayId}`;
      io.to(roomName).emit(eventType, eventData);
      console.log(`📤 تم إرسال ${eventType} للغرفة ${roomName}`);
    });

  } catch (error) {
    console.error('❌ خطأ في إرسال الحدث للغرف المناسبة:', error);
    // في حالة الخطأ، إرسال لجميع العملاء كخطة احتياطية
    io.emit(eventType, eventData);
  }
}

// ===== OVERLAY BAR WEBSOCKET SYSTEM =====
// نظام WebSocket بسيط للـ Overlay Bar
let overlayBarCounters = { wins: 0, losses: 0, lastUpdated: Date.now() };

// إنشاء namespace منفصل للـ Overlay Bar
const overlayBarNamespace = io.of('/overlay-bar');

overlayBarNamespace.on('connection', (socket) => {
  console.log(`🎮 Overlay Bar connected: ${socket.id}`);

  // إرسال القيم الحالية للعميل الجديد
  socket.emit('counters', overlayBarCounters);

  // استقبال تحديثات العدادات
  socket.on('updateCounters', (data) => {
    try {
      overlayBarCounters = {
        wins: Math.max(0, data.wins || 0),
        losses: Math.max(0, data.losses || 0),
        lastUpdated: Date.now()
      };

      // إرسال التحديث لجميع العملاء المتصلين
      overlayBarNamespace.emit('counters', overlayBarCounters);

      console.log(`📊 Overlay Bar updated: Wins=${overlayBarCounters.wins}, Losses=${overlayBarCounters.losses}`);
    } catch (error) {
      console.error('❌ خطأ في تحديث Overlay Bar:', error);
    }
  });

  // طلب القيم الحالية
  socket.on('getCounters', () => {
    socket.emit('counters', overlayBarCounters);
  });

  socket.on('disconnect', () => {
    console.log(`🎮 Overlay Bar disconnected: ${socket.id}`);
  });
});


// Socket.IO connection
io.on('connection', (socket) => {
  console.log(`🔌 A user connected (Socket: ${socket.id})`);

  // تسجيل session جديد
  userSessions.set(socket.id, {
    socketId: socket.id,
    userId: null,
    connectedAt: new Date()
  });

  console.log(`📊 Total sessions after connection: ${userSessions.size}`);

  // معالج عام لتشخيص جميع الأحداث
  socket.onAny((eventName, ...args) => {
    if (eventName === 'userAuthenticated') {
      console.log(`🎯 Received event: ${eventName}`, args[0]);
    }
  });

  // Send current connection status
  socket.emit('connectionStatus', {
    connected: tiktokConnection !== null,
    username: activeUsername
  });

  // Send current gift mappings
  socket.emit('giftMappingsUpdated', { mappings: giftMappings });

  // Send TikTok gift data
  socket.emit('tikTokGiftsData', { gifts: tikTokGifts });

  // Send current display settings
  socket.emit('displaySettingsUpdated', { settings: displaySettings });

  // Send current profiles
  socket.emit('profilesUpdated', {
    profiles: profiles,
    activeProfile: activeProfile
  });

  // إرسال إعدادات TTS الحالية
  socket.emit('ttsSettings', ttsSettings);

  // نظام إدارة الإعدادات الجديد
  socket.on('getAllSettings', (callback) => {
    try {
      // تجميع جميع الإعدادات في كائن واحد
      const allSettings = {
        general: {
          theme: 'light',
          language: 'ar'
        },
        profiles: {
          profiles: profiles,
          activeProfile: activeProfile
        },
        giftMappings: {
          mappings: giftMappings
        },
        overlay: displaySettings,
        tts: ttsSettings
      };

      callback({ success: true, data: allSettings });
    } catch (error) {
      console.error('Error getting settings:', error);
      callback({ success: false, error: error.message });
    }
  });

  // حفظ الإعدادات
  socket.on('saveSettings', (data) => {
    try {
      const { type, data: settingsData } = data;

      switch (type) {
        case 'general':
          // حفظ الإعدادات العامة
          console.log('حفظ الإعدادات العامة:', settingsData);
          break;

        case 'profiles':
          // تحديث الملفات الشخصية
          if (settingsData && settingsData.profiles) {
            profiles = settingsData.profiles;
            saveProfilesList();
          }
          break;

        case 'activeProfile':
          // تغيير الملف الشخصي النشط
          if (settingsData && settingsData.id) {
            setActiveProfile(settingsData.id);
          }
          break;

        case 'giftMappings':
          // تحديث تعيينات الهدايا
          if (settingsData && settingsData.mappings) {
            giftMappings = JSON.parse(JSON.stringify(settingsData.mappings));
            saveProfileData(activeProfile);
          }
          break;

        case 'tts':
          // تحديث إعدادات TTS
          if (settingsData) {
            const { updateTtsSettings } = require('./speakText-edge-tts.js');
            updateTtsSettings(settingsData);
            // حفظ إعدادات TTS في ملف البروفايل النشط
            saveProfileData(activeProfile);
          }
          break;

        case 'overlay':
          // تحديث إعدادات العرض
          if (settingsData) {
            displaySettings = settingsData;
            saveProfileData(activeProfile);
          }
          break;

        default:
          console.warn(`Unknown settings type: ${type}`);
      }

      // إرسال الإعدادات المحدثة لجميع العملاء
      io.emit('settingsUpdated', { type, data: settingsData });

    } catch (error) {
      console.error('Error saving settings:', error);
    }
  });

  // Send available gifts for gift mapping
  socket.on('getAvailableGifts', () => {
    console.log(`Sending ${tikTokGifts.length} gifts to client`);
    socket.emit('availableGifts', tikTokGifts);
  });

  // Get current gift mappings
  socket.on('getGiftMappings', () => {
    loadProfileData(activeProfile);
    socket.emit('giftMappings', { mappings: giftMappings });
  });

  // Handle file upload for gift actions
  socket.on('uploadFile', (data) => {
    try {
      const { fileName, fileContent } = data;
      const fileData = fileContent.split(',')[1];
      const buffer = Buffer.from(fileData, 'base64');

      // تحديد امتداد الملف بناءً على نوع الملف (غير مستخدم حاليًا)

      // إنشاء اسم فريد للملف
      const uniqueFileName = `${Date.now()}-${fileName}`;
      const filePath = path.join(uploadsDir, uniqueFileName);

      // حفظ الملف
      fs.writeFileSync(filePath, buffer);

      // إرسال مسار الملف للعميل
      socket.emit('fileUploaded', {
        success: true,
        filePath: `/uploads/${uniqueFileName}`,
        fileName: uniqueFileName
      });

      console.log(`File uploaded: ${uniqueFileName}`);
    } catch (error) {
      console.error('Error uploading file:', error);
      socket.emit('fileUploaded', { success: false, error: error.message });
    }
  });

  // دالة لتشغيل الصوت على الخادم باستخدام مكتبة sound-play (داخل البرنامج)
  function playSound(soundFilePath) {
    if (!soundFilePath) return;

    try {
      console.log(`تشغيل الصوت على الخادم: ${soundFilePath}`);

      // تحديد المسار الكامل للملف
      let fullPath = soundFilePath;
      if (soundFilePath.startsWith('/')) {
        fullPath = path.join(__dirname, 'public', soundFilePath);
      } else {
        fullPath = path.join(__dirname, 'public', '/', soundFilePath);
      }

      // التحقق من وجود الملف
      if (!fs.existsSync(fullPath)) {
        console.error(`ملف الصوت غير موجود: ${fullPath}`);
        return;
      }

      // استخدام مكتبة sound-play لتشغيل الصوت داخل البرنامج
      const Sound = require('sound-play');

      // تشغيل الصوت مع مستوى صوت 80%
      Sound.play(fullPath, 0.8)
        .then(() => {
          console.log(`تم تشغيل الصوت بنجاح (داخل البرنامج): ${soundFilePath}`);
        })
        .catch(err => {
          console.error('خطأ في تشغيل الصوت باستخدام sound-play:', err);

          // محاولة بديلة باستخدام play-sound
          try {
            const player = require('play-sound')(opts = {});

            // تحديد الخيارات حسب نظام التشغيل
            let playerOptions = {};

            if (process.platform === 'win32') {
              // Windows - استخدام مشغل صوت مناسب
              playerOptions = { timeout: 300000 }; // 5 دقائق كحد أقصى للتشغيل
            } else if (process.platform === 'darwin') {
              // macOS
              playerOptions = { afplay: ['-v', '1'] }; // مستوى الصوت
            } else {
              // Linux
              playerOptions = { aplay: ['-D', 'default'] }; // استخدام جهاز الصوت الافتراضي
            }

            // تشغيل الصوت
            player.play(fullPath, playerOptions, (err2) => {
              if (err2) {
                console.error('فشلت المحاولة البديلة لتشغيل الصوت باستخدام play-sound:', err2);

                // محاولة أخيرة باستخدام الطرق الأصلية
                try {
                  const { exec } = require('child_process');
                  if (process.platform === 'win32') {
                    // Windows
                    exec(`powershell -c (New-Object Media.SoundPlayer "${fullPath}").PlaySync();`);
                    console.log(`تم تشغيل الصوت بنجاح (باستخدام PowerShell): ${soundFilePath}`);
                  } else if (process.platform === 'darwin') {
                    // macOS
                    exec(`afplay "${fullPath}"`);
                    console.log(`تم تشغيل الصوت بنجاح (باستخدام afplay): ${soundFilePath}`);
                  } else {
                    // Linux
                    exec(`aplay "${fullPath}"`);
                    console.log(`تم تشغيل الصوت بنجاح (باستخدام aplay): ${soundFilePath}`);
                  }
                } catch (finalError) {
                  console.error('فشلت جميع محاولات تشغيل الصوت:', finalError);
                }
              } else {
                console.log(`تم تشغيل الصوت بنجاح (باستخدام play-sound): ${soundFilePath}`);
              }
            });
          } catch (fallbackErr) {
            console.error('فشلت محاولة تشغيل الصوت البديلة:', fallbackErr);
          }
        });
    } catch (err) {
      console.error('خطأ في تشغيل الصوت:', err);

      // محاولة أخيرة باستخدام الطرق الأصلية
      try {
        const { exec } = require('child_process');
        if (process.platform === 'win32') {
          // Windows
          exec(`powershell -c (New-Object Media.SoundPlayer "${fullPath}").PlaySync();`);
          console.log(`تم تشغيل الصوت بنجاح (باستخدام PowerShell): ${soundFilePath}`);
        } else if (process.platform === 'darwin') {
          // macOS
          exec(`afplay "${fullPath}"`);
          console.log(`تم تشغيل الصوت بنجاح (باستخدام afplay): ${soundFilePath}`);
        } else {
          // Linux
          exec(`aplay "${fullPath}"`);
          console.log(`تم تشغيل الصوت بنجاح (باستخدام aplay): ${soundFilePath}`);
        }
      } catch (fallbackError) {
        console.error('فشلت جميع محاولات تشغيل الصوت:', fallbackError);
      }
    }
  }

  // اختبار الإجراء
  socket.on('testAction', (data) => {
    try {
      const { type, duration, overlayId, soundFile, fileContent, actions, filePath, playSoundOnServer } = data;

      // معالجة ملف الوسائط إذا كان موجوداً
      let finalFilePath = filePath || '';
      console.log('معالجة ملف الوسائط، المسار الأصلي:', filePath);

      if (fileContent) {
        console.log('تم استلام محتوى ملف، جاري معالجته...');
        const fileData = fileContent.split(',')[1];
        const buffer = Buffer.from(fileData, 'base64');
        const fileType = fileContent.split(';')[0].split(':')[1];
        console.log('نوع الملف المستلم:', fileType);

        // تحديد امتداد الملف
        let fileExtension = '.bin';
        if (fileType.startsWith('image/')) {
          fileExtension = '.' + fileType.replace('image/', '');
          console.log('تم تحديد امتداد الصورة:', fileExtension);
        } else if (fileType.startsWith('video/')) {
          fileExtension = '.' + fileType.replace('video/', '');
          console.log('تم تحديد امتداد الفيديو:', fileExtension);
        }

        // إنشاء اسم ملف مؤقت للاختبار
        const tempFileName = `test-action-${Date.now()}${fileExtension}`;
        const tempFilePath = path.join(uploadsDir, tempFileName);
        console.log('مسار الملف المؤقت:', tempFilePath);

        try {
          // حفظ الملف
          fs.writeFileSync(tempFilePath, buffer);
          finalFilePath = `/uploads/${tempFileName}`;
          console.log('تم حفظ الملف بنجاح، المسار النهائي:', finalFilePath);

          // التحقق من وجود الملف
          if (fs.existsSync(tempFilePath)) {
            const stats = fs.statSync(tempFilePath);
            console.log('حجم الملف المحفوظ:', stats.size, 'بايت');
          } else {
            console.error('لم يتم العثور على الملف بعد الحفظ!');
          }
        } catch (err) {
          console.error('خطأ في حفظ الملف:', err);
        }
      } else {
        console.log('استخدام مسار الملف الموجود:', finalFilePath);
      }

      // تشغيل الصوت على الخادم إذا كان الخيار مفعلاً
      const soundPath = soundFile || data.soundFilePath;
      if (playSoundOnServer && soundPath && (actions && actions.includes('sound') || type === 'sound')) {
        console.log(`تشغيل الصوت على الخادم (من اختبار الإجراء): ${soundPath}`);
        // إرسال إشعار للعميل بأن الصوت سيتم تشغيله على الخادم
        socket.emit('serverMessage', { type: 'info', message: 'جاري تشغيل الصوت على الخادم...' });

        // تشغيل الصوت
        playSound(soundPath);

        // إرسال إشعار بنجاح تشغيل الصوت
        socket.emit('serverMessage', { type: 'success', message: 'تم تشغيل الصوت على الخادم بنجاح' });
      }

      // إرسال الإجراء للاختبار إلى شاشة العرض المحددة فقط إذا كان هناك عناصر مرئية للعرض
      // أو إذا كان الصوت سيتم تشغيله على شاشة العرض (وليس على الخادم)
      // أو إذا كان هناك نص مخصص للعرض
      const hasVisualElements = actions && (actions.includes('image') || actions.includes('video'));
      const needsOverlayForSound = actions && actions.includes('sound') && !playSoundOnServer;
      const hasCustomText = actions && actions.includes('text') && data.customText;

      if (hasVisualElements || needsOverlayForSound || hasCustomText) {
        const testData = {
          giftId: 'test',
          giftName: 'اختبار',
          nickname: 'مستخدم اختبار',
          type,
          duration: duration || 5,
          soundFile: soundPath,
          mediaFilePath: finalFilePath,
          filePath: finalFilePath,
          actions: actions || [type],
          testMode: true,
          playSoundOnServer: playSoundOnServer, // إضافة خيار تشغيل الصوت على الخادم
          customText: data.customText || '' // إضافة النص المخصص من البيانات المستلمة
        };

        console.log('بيانات اختبار النص المخصص:', {
          hasTextAction: actions && actions.includes('text'),
          customText: data.customText,
          sentCustomText: testData.customText
        });

        console.log('إرسال بيانات اختبار إلى الشاشة:', testData);

        // إرسال البيانات إلى شاشة العرض المحددة أو جميع شاشات العرض
        if (overlayId && overlayId !== 'default') {
          io.to(`overlay-${overlayId}`).emit('testGift', testData);
        } else {
          // إرسال إلى جميع شاشات العرض
          io.emit('testGift', testData);
        }
      } else {
        console.log('لا حاجة لإرسال بيانات إلى شاشة العرض، سيتم تشغيل الصوت على الخادم فقط');
      }

      // إشعار المستخدم بنجاح الاختبار
      socket.emit('testActionResult', { success: true });

      console.log(`Action test sent: ${type}, to overlay: ${overlayId || 'default'}`);
    } catch (error) {
      console.error('Error testing action:', error);
      socket.emit('testActionResult', { success: false, error: error.message });
    }
  });

  // انضمام لغرفة شاشة العرض
  socket.on('joinOverlayRoom', (data) => {
    const { overlayId } = data;

    // مغادرة جميع غرف العرض أولاً
    Object.keys(socket.rooms).forEach(room => {
      if (room.startsWith('overlay-')) {
        socket.leave(room);
      }
    });

    // الانضمام للغرفة المختارة
    const roomName = `overlay-${overlayId || 'default'}`;
    socket.join(roomName);
    console.log(`Client joined overlay room: ${roomName}`);

    // إرسال تعيينات الهدايا الحالية للعميل المنضم
    socket.emit('giftMappingsUpdated', { mappings: giftMappings });
  });

  // Handle connect to TikTok Live
  socket.on('connectToTikTok', async (data) => {
    const { username } = data;
    saveLastUsername(username); // حفظ الاسم في ملف

    console.log(`Attempting to connect to @${username}'s TikTok LIVE...`);

    // Disconnect existing connection if any
    if (tiktokConnection) {
      tiktokConnection.disconnect();
      tiktokConnection = null;
      activeUsername = '';
    }

    // إعدادات الاتصال
    const connectionOptions = {
      enableExtendedGiftInfo: true,
      enableWebsocketUpgrade: true,
      enableRequestPollingFallback: true,
      requestPollingIntervalMs: 2000,
      clientParams: {
        "app_language": "ar-SA",
        "device_platform": "web"
      },
      processInitialData: true,
      fetchRoomInfoOnConnect: true
    };

    try {
      // Create a new connection باستخدام اسم المستخدم فقط
      tiktokConnection = new WebcastPushConnection(username, connectionOptions);

      // Add debug event listener
      tiktokConnection.on('debug', (message) => {
        console.log(`[TikTok Debug] ${message}`);
      });

      // Add warning event listener
      tiktokConnection.on('warning', (message) => {
        console.warn(`[TikTok Warning] ${message}`);
      });

      // ======================
      // 1. نظام منع تكرار الأحداث (de-duplication)
      // ======================
      const recentEventCache = new Map();

      function isDuplicate(key, timeout = 3000) {
        const now = Date.now();
        if (recentEventCache.has(key)) {
          const last = recentEventCache.get(key);
          if (now - last < timeout) return true;
        }
        recentEventCache.set(key, now);
        return false;
      }

      // تنظيف مخزن الأحداث كل 5 دقائق
      setInterval(() => {
        const now = Date.now();
        recentEventCache.forEach((timestamp, key) => {
          if (now - timestamp > 5 * 60 * 1000) { // 5 دقائق
            recentEventCache.delete(key);
          }
        });
        console.log(`تم تنظيف مخزن الأحداث، عدد المفاتيح المتبقية: ${recentEventCache.size}`);
      }, 5 * 60 * 1000); // كل 5 دقائق

      // ======================
      // 2. نظام طوابير لكل مستخدم + هدية (queue per user-gift)
      // ======================
      const eventQueues = {};

      /**
       * انشئ طابور جديد أو استعمل الموجود
       * @param {string} key - معرف فريد لكل مستخدم + نوع الحدث
       * @param {number} delay - تأخير زمني بين تنفيذ كل حدث (بالمللي ثانية)
       * @returns {function} دالة لإضافة حدث جديد إلى الطابور
       */
      function createEventQueue(key, delay = 100) {
        if (!eventQueues[key]) {
          eventQueues[key] = {
            queue: [],
            processing: false
          };
        }

        const processQueue = () => {
          const queueObj = eventQueues[key];
          if (queueObj.processing || queueObj.queue.length === 0) return;

          queueObj.processing = true;
          const eventData = queueObj.queue.shift();

          handleSingleEvent(eventData).finally(() => {
            queueObj.processing = false;
            setTimeout(processQueue, delay);
          });
        };

        return (eventData) => {
          eventQueues[key].queue.push(eventData);
          processQueue();
        };
      }

      /**
       * معالجة حدث واحد
       * @param {Object} eventData - بيانات الحدث
       */
      async function handleSingleEvent(eventData) {
        try {
          const { type, data, mapping } = eventData;
          console.log(`🎁 معالجة حدث ${type} من ${data.nickname || data.uniqueId}`);

          // فحص الاشتراك - فقط للمستخدمين غير المشتركين
          if (subscriptionStatus.currentUserId && !subscriptionStatus.hasSubscription) {
            // قائمة التعيينات المسموحة (أول 5 فقط)
            const allowedMappingIds = subscriptionStatus.allowedMappings || [];
            if (!allowedMappingIds.includes(mapping.id)) {
              console.log('⚠️ تم منع تنفيذ التعيين - تجاوز الحد المجاني (5 تعيينات)');
              return;
            }
          }

          // إنشاء مصفوفة للعمليات المتوازية
          const promises = [];

          // تنفيذ محاكاة ضغط المفاتيح إذا كان موجودًا (بدون انتظار)
          if (mapping && mapping.keypressSequence && Array.isArray(mapping.keypressSequence) && mapping.keypressSequence.length > 0) {
            console.log(`تنفيذ تسلسل مفاتيح من تعيين ${type}`);
            promises.push(simulateKeyPressSequence(mapping.keypressSequence));
          }

          // تشغيل الصوت على الخادم إذا كان الخيار مفعلاً (بدون انتظار)
          if (mapping && mapping.playSoundOnServer && mapping.actions && mapping.actions.includes('sound') && mapping.soundFilePath) {
            console.log(`تشغيل صوت على الخادم للحدث ${type}: ${data.nickname || data.uniqueId}`);
            promises.push(playSound(mapping.soundFilePath));
          }

          // تحديد ما إذا كان يجب إرسال الإجراء إلى شاشة العرض (فوراً - بدون انتظار)
          if (mapping) {
            const hasVisualElements = mapping.actions && (mapping.actions.includes('image') || mapping.actions.includes('video'));
            const needsOverlayForSound = mapping.actions && mapping.actions.includes('sound') && !mapping.playSoundOnServer;
            const hasCustomText = mapping.actions && mapping.actions.includes('text') && mapping.customText;
            const hasUserDisplay = mapping.actions && mapping.actions.includes('user-display');

            if (hasVisualElements || needsOverlayForSound || hasCustomText || hasUserDisplay) {
              // إرسال بيانات الإجراء إلى شاشة العرض فوراً
              const actionData = {
                type: mapping.actions && mapping.actions.includes('sound') ? 'sound' :
                  (mapping.actions && mapping.actions.includes('image') ? 'image' :
                    (mapping.actions && mapping.actions.includes('video') ? 'video' : 'alert')),
                eventType: type,
                nickname: data.nickname,
                uniqueId: data.uniqueId,
                profilePictureUrl: data.profilePictureUrl || null, // إضافة صورة المستخدم
                actions: mapping.actions || [],
                duration: mapping.duration || 5,
                overlayId: mapping.overlayId || 'default',
                mediaFilePath: mapping.mediaFilePath,
                soundFilePath: mapping.soundFilePath,
                message: getEventMessage(type, data),
                playSoundOnServer: mapping.playSoundOnServer,
                customText: mapping.customText || '', // إضافة النص المخصص
                userDisplaySettings: mapping.userDisplaySettings || null // إضافة إعدادات عرض صورة المستخدم
              };

              // إضافة بيانات إضافية حسب نوع الحدث
              if (type === 'gift') {
                actionData.giftId = data.giftId;
                actionData.giftName = data.giftName;
                actionData.repeatCount = data.repeatCount;
                actionData.diamondCount = data.diamondCount;
              } else if (type === 'like') {
                actionData.likeCount = data.likeCount;
                actionData.totalLikeCount = data.totalLikeCount;
              } else if (type === 'comment') {
                actionData.comment = data.comment;
              }

              // إرسال البيانات إلى شاشة العرض المحددة أو جميع شاشات العرض (فوراً)
              if (mapping.overlayId && mapping.overlayId !== 'default') {
                io.to(`overlay-${mapping.overlayId}`).emit('eventAction', actionData);
              } else {
                io.emit('eventAction', actionData);
              }
            } else {
              console.log(`لا حاجة لإرسال بيانات إلى شاشة العرض للحدث ${type}، سيتم تشغيل الصوت على الخادم فقط`);
            }
          }

          // انتظار انتهاء جميع العمليات المتوازية (الضغطات والأصوات)
          await Promise.all(promises);
        } catch (err) {
          console.error(`خطأ في معالجة الحدث: ${err.message}`);
        }
      }

      /**
       * الحصول على رسالة الحدث حسب نوعه
       * @param {string} type - نوع الحدث
       * @param {Object} data - بيانات الحدث
       * @returns {string} رسالة الحدث
       */
      function getEventMessage(type, data) {
        switch (type) {
          case 'gift':
            return `${data.nickname || data.uniqueId} أرسل هدية ${data.giftName}!`;
          case 'like':
            return `${data.nickname || data.uniqueId} وصل إلى ${data.userTotalLikeCount || data.likeCount} إعجاب!`;
          case 'follow':
            return `${data.nickname || data.uniqueId} تابع البث!`;
          case 'share':
            return `${data.nickname || data.uniqueId} شارك البث!`;
          case 'join':
            return `${data.nickname || data.uniqueId} انضم إلى البث!`;
          case 'comment':
            return `${data.nickname || data.uniqueId}: ${data.comment}`;
          default:
            return `${data.nickname || data.uniqueId} أرسل حدث ${type}!`;
        }
      }

      // Connect to TikTok LIVE
      await tiktokConnection.connect();

      console.log(`Successfully connected to @${username}'s TikTok LIVE`);
      activeUsername = username;

      // Notify all clients about successful connection
      io.emit('connectionStatus', {
        connected: true,
        username: username
      });

      // Define event handlers for TikTok events

      // Gift event
      tiktokConnection.on('gift', (data) => {
        // منع تكرار الأحداث
        const eventKey = `gift_${data.uniqueId}_${data.giftId}_${data.repeatCount}`;
        if (isDuplicate(eventKey, 1000)) {
          console.log(`تجاهل حدث هدية متكرر من ${data.uniqueId}`);
          return;
        }

        // معالجة هدايا التكرار (repeatCount)
        // إذا كانت الهدية من نوع التكرار وليست نهاية التكرار، نتجاهلها ونعالج فقط الهدية النهائية
        if (data.repeatEnd === false) {
          console.log(`تجاهل حدث هدية وسيطة (غير مكتملة) من ${data.uniqueId} - repeatCount: ${data.repeatCount}`);
          return;
        }

        // طباعة معلومات تشخيصية عن الهدية
        console.log(`استلام هدية: ${data.giftName || data.giftId} من ${data.nickname}, repeatCount: ${data.repeatCount}, repeatEnd: ${data.repeatEnd}`);

        console.log(`${data.uniqueId} sent ${data.giftName}`);

        const giftData = {
          uniqueId: data.uniqueId,
          nickname: data.nickname,
          giftId: data.giftId,
          giftName: data.giftName,
          repeatCount: data.repeatCount,
          diamondCount: data.diamondCount
        };

        // Find the gift image from our database if available
        const giftInfo = tikTokGifts.find(g => g.id === data.giftId || g.name === data.giftName);
        if (giftInfo) {
          giftData.imageUrl = giftInfo.imageUrl;
        }

        try {
          loadProfileData(activeProfile);
          let mapping = null;
          if (data.giftId && giftMappings && Array.isArray(giftMappings) && giftMappings.length > 0) {
            mapping = giftMappings.find(m => String(m.giftId) === String(data.giftId));
          }
          if (!mapping && data.giftName && giftMappings && Array.isArray(giftMappings) && giftMappings.length > 0) {
            mapping = giftMappings.find(m =>
              m.giftName &&
              m.giftName.trim().toLowerCase() === data.giftName.trim().toLowerCase()
            );
          }
          if (!mapping && giftMappings && Array.isArray(giftMappings) && giftMappings.length > 0) {
            mapping = giftMappings.find(m => m.giftId === 'any');
          }
          if (mapping) {

            // إنشاء مفتاح فريد للطابور: لكل مستخدم + هدية نوعها مختلف
            const queueKey = `gift_${data.uniqueId}_${data.giftId}`;

            // إنشاء طابور للمستخدم والهدية إذا لم يكن موجودًا
            const eventDuration = (mapping.duration || 5) * 1000; // تحويل الثواني إلى ملي ثانية
            const pushToQueue = createEventQueue(queueKey, eventDuration); // تأخير بناءً على مدة الحدث

            // 👇 بدلًا من تنفيذ مرة واحدة، ننفذ بعدد التكرارات
            const repeatTimes = data.repeatCount || 1;
            console.log(`سيتم تنفيذ الإجراء ${repeatTimes} مرة للهدية ${data.giftName || data.giftId}`);

            for (let i = 0; i < repeatTimes; i++) {
              pushToQueue({
                type: 'gift',
                data: { ...data, repeatCount: 1 }, // لكل مرة، نعتبرها هدية واحدة
                mapping: mapping
              });
            }
          }
        } catch (err) {
          console.error('خطأ في تنفيذ إجراء الخادم للهدايا:', err);
        }
        // إرسال الحدث للغرف المناسبة بناءً على التعيينات
        sendEventToAppropriateOverlays('gift', giftData);
      });

      // Like event
      tiktokConnection.on('like', (data) => {
        const userId = data.uniqueId || data.userId;
        const userNickname = data.nickname || userId;

        // منع تكرار الأحداث المتطابقة تمامًا
        const eventKey = `like_${userId}_${data.likeCount}`;
        if (isDuplicate(eventKey, 1000)) {
          console.log(`تجاهل حدث إعجاب متكرر من ${userNickname}`);
          return;
        }

        // تهيئة عداد الإعجابات للمستخدم إذا لم يكن موجودًا
        if (!userLikeCounts[userId]) {
          userLikeCounts[userId] = 0;
        }

        // إضافة عدد الإعجابات الجديدة
        userLikeCounts[userId] += data.likeCount;
        const userTotalLikes = userLikeCounts[userId];

        console.log(`${userNickname} أرسل ${data.likeCount} إعجاب، إجمالي المستخدم: ${userTotalLikes}, إجمالي البث: ${data.totalLikeCount || 'غير متوفر'}`);

        // إرسال بيانات الإعجاب للعملاء
        const likeData = {
          uniqueId: data.uniqueId,
          nickname: userNickname,
          likeCount: data.likeCount,
          userTotalLikeCount: userTotalLikes,
          totalLikeCount: data.totalLikeCount || data.likeCount
        };

        // إرسال الحدث للغرف المناسبة بناءً على التعيينات
        sendEventToAppropriateOverlays('like', likeData);

        // معالجة تعيينات الإعجابات
        try {
          loadProfileData(activeProfile);

          // البحث عن تعيينات الإعجابات
          if (giftMappings && Array.isArray(giftMappings)) {
            const likeMappings = giftMappings.filter(m => m.eventType === 'like');

            likeMappings.forEach(mapping => {
              // تحويل قيمة العتبة إلى رقم صحيح
              const likeThreshold = parseInt(mapping.likeCount) || 10;

              // تأكد من أن العتبة رقم موجب
              if (likeThreshold <= 0) {
                console.log(`تجاهل تعيين إعجابات بعتبة غير صالحة: ${mapping.likeCount}`);
                return;
              }

              // تهيئة مخزن آخر عدد إعجابات تمت معالجته
              if (!mapping.lastProcessedLikeCounts) {
                mapping.lastProcessedLikeCounts = {};
              }

              if (mapping.lastProcessedLikeCounts[userId] === undefined) {
                mapping.lastProcessedLikeCounts[userId] = 0;
              }

              const lastCount = mapping.lastProcessedLikeCounts[userId];

              // طباعة معلومات تشخيصية
              console.log(`معلومات الإعجابات للمستخدم ${userNickname}:`);
              console.log(`- إجمالي الإعجابات: ${userTotalLikes}`);
              console.log(`- آخر عدد تمت معالجته: ${lastCount}`);
              console.log(`- العتبة المحددة: ${likeThreshold}`);

              // التحقق مما إذا كان الفرق بين إجمالي الإعجابات وآخر عدد تمت معالجته يساوي أو يتجاوز العتبة
              // هذا يضمن أن الإجراء يتم تنفيذه فقط عند تجاوز العتبة
              if (userTotalLikes - lastCount >= likeThreshold) {
                // حساب العتبة التالية بإضافة قيمة العتبة إلى آخر عدد تمت معالجته
                // هذا يضمن أن العتبة التالية هي دائمًا مضاعف للعتبة المحددة
                const newProcessedCount = lastCount + likeThreshold;

                console.log(`✅ تم الوصول إلى العتبة الجديدة! إجمالي الإعجابات: ${userTotalLikes}, العتبة: ${likeThreshold}`);
                console.log(`✅ تحديث عداد المعالجة: ${lastCount} -> ${newProcessedCount}`);

                // تحديث آخر عدد إعجابات تم معالجته
                mapping.lastProcessedLikeCounts[userId] = newProcessedCount;

                console.log(`✅ إعجابات متبقية غير مكتملة: ${userTotalLikes - newProcessedCount}`);
                console.log(`✅ العتبة التالية: ${newProcessedCount + likeThreshold}`);

                // تنفيذ الإجراء للعتبة الجديدة
                console.log(`تنفيذ الإجراء للعتبة ${newProcessedCount} (${likeThreshold} إعجاب) من ${userNickname}`);

                // إنشاء مفتاح فريد للطابور
                const queueKey = `like_${userId}_threshold_${newProcessedCount}`;

                // إنشاء طابور للمستخدم والحدث
                const eventDuration = (mapping.duration || 5) * 1000;
                const pushToQueue = createEventQueue(queueKey, eventDuration);

                // إضافة الحدث للطابور للتنفيذ المتسلسل
                pushToQueue({
                  type: 'like',
                  data: {
                    ...data,
                    userTotalLikeCount: userTotalLikes,
                    likeCount: likeThreshold,
                    processedCount: newProcessedCount
                  },
                  mapping: mapping
                });
              } else {
                // حساب العتبة التالية
                const nextThreshold = lastCount + likeThreshold;
                console.log(`⏳ لم يتم الوصول بعد إلى العتبة التالية (${nextThreshold})، الإعجابات الحالية: ${userTotalLikes}`);
                console.log(`⏳ متبقي ${nextThreshold - userTotalLikes} إعجاب للوصول إلى العتبة التالية`);
              }
            });

            // حفظ بيانات الملف الشخصي بعد تحديث أي من عدادات الإعجابات
            saveProfileData(activeProfile);
          }
        } catch (err) {
          console.error('خطأ في تنفيذ إجراء الإعجابات:', err);
          console.error(err.stack);
        }
      });

      // Follow event
      tiktokConnection.on('follow', (data) => {
        // منع تكرار الأحداث
        const eventKey = `follow_${data.uniqueId}`;
        if (isDuplicate(eventKey, 3000)) {
          console.log(`تجاهل حدث متابعة متكرر من ${data.uniqueId}`);
          return;
        }

        console.log(`${data.uniqueId} (${data.nickname}) followed the streamer`);

        // إرسال بيانات المتابعة للعميل
        const followData = {
          uniqueId: data.uniqueId,
          nickname: data.nickname
        };

        io.emit('follow', followData);

        // إرسال تنبيه المتابعة لشاشات العرض
        const alertData = {
          type: 'alert',
          alertType: 'follow',
          message: `${data.nickname || data.uniqueId} تابع البث!`,
          position: 'top',
          duration: 4000
        };

        io.emit('alert', alertData);

        // معالجة تعيينات المتابعة
        try {
          loadProfileData(activeProfile);

          // البحث عن تعيينات المتابعة
          if (giftMappings && Array.isArray(giftMappings) && giftMappings.length > 0) {
            const followMappings = giftMappings.filter(m => m.eventType === 'follow');

            if (followMappings.length > 0) {
              // التحقق من كل تعيين متابعة
              followMappings.forEach(mapping => {
                console.log(`تنفيذ إجراء متابعة من قبل ${data.nickname}`);

                // إنشاء مفتاح فريد للطابور: لكل مستخدم + نوع الحدث
                const queueKey = `join_${data.uniqueId}`;

                // إنشاء طابور للمستخدم والحدث إذا لم يكن موجودًا
                const eventDuration = (mapping.duration || 5) * 1000;
                const pushToQueue = createEventQueue(queueKey, eventDuration); // تأخير بناءً على مدة الحدث
                hToQueue = createEventQueue(queueKey, eventDuration); // تأخير بناءً على مدة الحدث

                // إضافة الحدث للطابور للتنفيذ المتسلسل
                pushToQueue({
                  type: 'follow',
                  data: data,
                  mapping: mapping
                });
              });
            }
          }
        } catch (err) {
          console.error('خطأ في تنفيذ إجراء المتابعة:', err);
        }
      });

      // Comment event
      tiktokConnection.on('comment', (data) => {
        // منع تكرار الأحداث
        const eventKey = `comment_${data.uniqueId}_${data.comment}`;
        if (isDuplicate(eventKey, 3000)) {
          console.log(`تجاهل حدث تعليق متكرر من ${data.uniqueId}`);
          return;
        }

        // إرسال التعليق للعملاء
        const commentData = {
          uniqueId: data.uniqueId,
          nickname: data.nickname,
          comment: data.comment
        };

        // إرسال الحدث للغرف المناسبة بناءً على التعيينات
        sendEventToAppropriateOverlays('comment', commentData);

        // معالجة أوامر الانضمام للفرق
        handleTeamJoinCommands(data, io);

        // إضافة التعليق لقائمة انتظار القراءة الصوتية
        if (ttsSettings.enabled) {
          filterAndQueueComment(data.comment, data.nickname || data.uniqueId);
        }

        // معالجة تعيينات التعليقات
        try {
          loadProfileData(activeProfile);

          // البحث عن تعيينات التعليقات
          if (giftMappings && Array.isArray(giftMappings) && giftMappings.length > 0) {
            const commentMappings = giftMappings.filter(m => m.eventType === 'comment');

            if (commentMappings.length > 0) {
              // التحقق من كل تعيين تعليقات
              commentMappings.forEach(mapping => {
                const commentText = mapping.commentText || '';

                // التحقق مما إذا كان التعليق يطابق النص المحدد
                if (commentText && data.comment && data.comment.trim().toLowerCase() === commentText.trim().toLowerCase()) {
                  console.log(`تم العثور على تعليق مطابق "${commentText}" من قبل ${data.nickname}`);

                  // إنشاء مفتاح فريد للطابور: لكل مستخدم + نوع الحدث
                  const queueKey = `comment_${data.uniqueId}`;

                  // إنشاء طابور للمستخدم والحدث إذا لم يكن موجودًا
                  const eventDuration = (mapping.duration || 5) * 1000;
                  const pushToQueue = createEventQueue(queueKey, eventDuration); // تأخير بناءً على مدة الحدث

                  // إضافة الحدث للطابور للتنفيذ المتسلسل
                  pushToQueue({
                    type: 'comment',
                    data: data,
                    mapping: mapping
                  });
                }
              });
            }
          }
        } catch (err) {
          console.error('خطأ في تنفيذ إجراء التعليقات:', err);
        }
      });

      // Chat event
      tiktokConnection.on('chat', (data) => {
        // منع تكرار الأحداث
        const eventKey = `chat_${data.uniqueId}_${data.comment}`;
        if (isDuplicate(eventKey, 3000)) {
          console.log(`تجاهل حدث دردشة متكرر من ${data.uniqueId}`);
          return;
        }

        console.log(`${data.uniqueId} (${data.nickname}): ${data.comment}`);

        // إرسال الدردشة للعملاء
        io.emit('chat', {
          uniqueId: data.uniqueId,
          nickname: data.nickname,
          comment: data.comment
        });

        // معالجة أوامر الانضمام للفرق
        handleTeamJoinCommands(data, io);

        // إضافة الدردشة لقائمة انتظار القراءة الصوتية
        if (ttsSettings.enabled) {
          filterAndQueueComment(data.comment, data.nickname || data.uniqueId);
        }

        // معالجة تعيينات التعليقات (أحياناً تأتي التعليقات كأحداث chat)
        try {
          // تحميل البروفايل النشط الصحيح
          const activeProfilePath = path.join(profilesDir, 'active_profile.json');
          if (fs.existsSync(activeProfilePath)) {
            activeProfile = fs.readFileSync(activeProfilePath, 'utf8').trim();
          }
          loadProfileData(activeProfile);

          // البحث عن تعيينات التعليقات
          if (giftMappings && Array.isArray(giftMappings) && giftMappings.length > 0) {
            const commentMappings = giftMappings.filter(m => m.eventType === 'comment');

            if (commentMappings.length > 0) {
              // التحقق من كل تعيين تعليقات
              commentMappings.forEach(mapping => {
                const commentText = mapping.commentText || '';

                // التحقق مما إذا كان التعليق يطابق النص المحدد
                if (commentText && data.comment && data.comment.trim().toLowerCase() === commentText.trim().toLowerCase()) {
                  console.log(`تم العثور على تعليق مطابق "${commentText}" من قبل ${data.nickname}`);

                  // إنشاء مفتاح فريد للطابور: لكل مستخدم + نوع الحدث
                  const queueKey = `comment_chat_${data.uniqueId}`;

                  // إنشاء طابور للمستخدم والحدث إذا لم يكن موجودًا
                  const eventDuration = (mapping.duration || 5) * 1000;
                  const pushToQueue = createEventQueue(queueKey, eventDuration); // تأخير بناءً على مدة الحدث

                  // إضافة الحدث للطابور للتنفيذ المتسلسل
                  pushToQueue({
                    type: 'comment',
                    data: data,
                    mapping: mapping
                  });
                }
              });
            }
          }
        } catch (err) {
          console.error('خطأ في تنفيذ إجراء التعليقات من أحداث الدردشة:', err);
        }
      });

      // Share event
      tiktokConnection.on('share', (data) => {
        // منع تكرار الأحداث
        const eventKey = `share_${data.uniqueId}`;
        if (isDuplicate(eventKey, 3000)) {
          console.log(`تجاهل حدث مشاركة متكرر من ${data.uniqueId}`);
          return;
        }

        const shareData = {
          uniqueId: data.uniqueId,
          nickname: data.nickname
        };

        io.emit('share', shareData);

        // معالجة تعيينات المشاركة
        try {
          loadProfileData(activeProfile);

          // البحث عن تعيينات المشاركة
          if (giftMappings && Array.isArray(giftMappings) && giftMappings.length > 0) {
            const shareMappings = giftMappings.filter(m => m.eventType === 'share');

            if (shareMappings.length > 0) {
              // التحقق من كل تعيين مشاركة
              shareMappings.forEach(mapping => {
                console.log(`تنفيذ إجراء مشاركة من قبل ${data.nickname}`);

                // إنشاء مفتاح فريد للطابور: لكل مستخدم + نوع الحدث
                const queueKey = `share_${data.uniqueId}`;

                // إنشاء طابور للمستخدم والحدث إذا لم يكن موجودًا
                const eventDuration = (mapping.duration || 5) * 1000;
                const pushToQueue = createEventQueue(queueKey, eventDuration); // تأخير بناءً على مدة الحدث

                // إضافة الحدث للطابور للتنفيذ المتسلسل
                pushToQueue({
                  type: 'share',
                  data: data,
                  mapping: mapping
                });
              });
            }
          }
        } catch (err) {
          console.error('خطأ في تنفيذ إجراء المشاركة:', err);
        }
      });

      // Join event
      tiktokConnection.on('join', (data) => {
        // منع تكرار الأحداث
        const eventKey = `join_${data.uniqueId}`;
        if (isDuplicate(eventKey, 3000)) {
          console.log(`تجاهل حدث انضمام متكرر من ${data.uniqueId}`);
          return;
        }

        const joinData = {
          uniqueId: data.uniqueId,
          nickname: data.nickname
        };

        io.emit('join', joinData);

        // معالجة تعيينات الانضمام
        try {
          loadProfileData(activeProfile);
          console.log(`فحص تعيينات الانضمام للمستخدم: ${data.nickname}`);

          // البحث عن تعيينات الانضمام
          if (giftMappings && Array.isArray(giftMappings) && giftMappings.length > 0) {
            const joinMappings = giftMappings.filter(m => m.eventType === 'join');
            console.log(`عدد تعيينات الانضمام الموجودة: ${joinMappings.length}`);

            if (joinMappings.length > 0) {
              // التحقق من كل تعيين انضمام
              joinMappings.forEach(mapping => {
                // التحقق من نوع الانضمام
                if (mapping.joinType === 'specific') {
                  // التحقق من اسم المستخدم المحدد
                  if (mapping.specificUsername &&
                    (data.nickname.toLowerCase() === mapping.specificUsername.toLowerCase() ||
                      data.uniqueId.toLowerCase() === mapping.specificUsername.toLowerCase())) {
                    console.log(`تنفيذ إجراء انضمام للمستخدم المحدد ${data.nickname}`);
                    executeJoinAction(mapping, data);
                  }
                } else {
                  // أي مستخدم
                  console.log(`تنفيذ إجراء انضمام من قبل ${data.nickname}`);
                  executeJoinAction(mapping, data);
                }
              });
            }
          }
        } catch (err) {
          console.error('خطأ في تنفيذ إجراء الانضمام:', err);
        }
      });

      // Member event (alternative way to detect joins)
      tiktokConnection.on('member', (data) => {
        console.log(`Member event received: ${data.uniqueId} (${data.nickname})`);

        const joinData = {
          uniqueId: data.uniqueId,
          nickname: data.nickname
        };

        io.emit('join', joinData);

        // معالجة تعيينات الانضمام أيضاً من حدث member
        try {
          loadProfileData(activeProfile);

          if (giftMappings && Array.isArray(giftMappings) && giftMappings.length > 0) {
            const joinMappings = giftMappings.filter(m => m.eventType === 'join');

            if (joinMappings.length > 0) {
              console.log(`تم العثور على ${joinMappings.length} تعيين انضمام من حدث member`);

              joinMappings.forEach(mapping => {
                if (mapping.joinType === 'specific') {
                  if (mapping.specificUsername &&
                    (data.nickname.toLowerCase() === mapping.specificUsername.toLowerCase() ||
                      data.uniqueId.toLowerCase() === mapping.specificUsername.toLowerCase())) {
                    console.log(`تنفيذ إجراء انضمام للمستخدم المحدد ${data.nickname} (member event)`);
                    executeJoinAction(mapping, data);
                  }
                } else {
                  console.log(`تنفيذ إجراء انضمام من قبل ${data.nickname} (member event)`);
                  executeJoinAction(mapping, data);
                }
              });
            }
          }
        } catch (err) {
          console.error('خطأ في تنفيذ إجراء الانضمام من حدث member:', err);
        }
      });

      // Social event (can include join events in some TikTok versions)
      tiktokConnection.on('social', (data) => {
        console.log(`Social event received: ${data.displayType || 'unknown'} from ${data.uniqueId} (${data.nickname})`);

        // Check if this is a join-related event
        if (data.displayType &&
          (data.displayType.toLowerCase().includes('join') ||
            data.displayType.toLowerCase().includes('enter') ||
            (data.label && (data.label.toLowerCase().includes('join') || data.label.toLowerCase().includes('entered'))))) {

          console.log(`Processing as join event: ${data.uniqueId} (${data.nickname})`);

          const joinData = {
            uniqueId: data.uniqueId,
            nickname: data.nickname
          };

          io.emit('join', joinData);

          // معالجة تعيينات الانضمام من حدث social
          try {
            loadProfileData(activeProfile);

            if (giftMappings && Array.isArray(giftMappings) && giftMappings.length > 0) {
              const joinMappings = giftMappings.filter(m => m.eventType === 'join');

              if (joinMappings.length > 0) {
                console.log(`تم العثور على ${joinMappings.length} تعيين انضمام من حدث social`);

                joinMappings.forEach(mapping => {
                  if (mapping.joinType === 'specific') {
                    if (mapping.specificUsername &&
                      (data.nickname.toLowerCase() === mapping.specificUsername.toLowerCase() ||
                        data.uniqueId.toLowerCase() === mapping.specificUsername.toLowerCase())) {
                      console.log(`تنفيذ إجراء انضمام للمستخدم المحدد ${data.nickname} (social event)`);
                      executeJoinAction(mapping, data);
                    }
                  } else {
                    console.log(`تنفيذ إجراء انضمام من قبل ${data.nickname} (social event)`);
                    executeJoinAction(mapping, data);
                  }
                });
              }
            }
          } catch (err) {
            console.error('خطأ في تنفيذ إجراء الانضمام من حدث social:', err);
          }
        } else {
          // Handle other social events if needed
          io.emit('social', {
            uniqueId: data.uniqueId,
            nickname: data.nickname,
            displayType: data.displayType,
            label: data.label
          });
        }
      });

      // متغيرات لتتبع إجراءات الانضمام الأخيرة لتجنب التكرار
      const recentJoinActions = {};
      let joinActionsCleanupInterval = null;

      // دالة مساعدة لتنفيذ إجراء الانضمام
      function executeJoinAction(mapping, data) {
        // تجنب تكرار تنفيذ الإجراء للمستخدم نفسه خلال فترة زمنية قصيرة
        const userId = data.uniqueId || data.userId;
        const currentTime = Date.now();

        // التحقق من وجود المستخدم في قائمة المستخدمين الذين تم تنفيذ إجراء لهم مؤخرًا
        if (recentJoinActions[userId]) {
          const timeSinceLastAction = currentTime - recentJoinActions[userId];
          // تجاهل الإجراء إذا تم تنفيذه خلال الـ 10 ثوانٍ الماضية
          if (timeSinceLastAction < 10000) {
            console.log(`تجاهل إجراء انضمام متكرر للمستخدم ${data.nickname || userId} (آخر إجراء منذ ${Math.floor(timeSinceLastAction / 1000)} ثوانٍ)`);
            return;
          }
        }

        // تسجيل وقت تنفيذ الإجراء لهذا المستخدم
        recentJoinActions[userId] = currentTime;

        // تنظيف قائمة الإجراءات القديمة كل 5 دقائق
        if (!joinActionsCleanupInterval) {
          joinActionsCleanupInterval = setInterval(() => {
            const now = Date.now();
            for (const user in recentJoinActions) {
              if (now - recentJoinActions[user] > 300000) { // 5 دقائق
                delete recentJoinActions[user];
              }
            }
          }, 300000); // كل 5 دقائق
        }

        // إنشاء مفتاح فريد للطابور: لكل مستخدم + نوع الحدث
        const queueKey = `join_${userId}`;

        // إنشاء طابور للمستخدم والحدث إذا لم يكن موجودًا
        const eventDuration = (mapping.duration || 5) * 1000; // تحويل الثواني إلى ملي ثانية
        const pushToQueue = createEventQueue(queueKey, eventDuration); // تأخير بناءً على مدة الحدث

        // إضافة الحدث للطابور للتنفيذ المتسلسل
        pushToQueue({
          type: 'join',
          data: data,
          mapping: mapping
        });

        console.log(`تم إضافة إجراء انضمام للمستخدم ${data.nickname || userId} (${mapping.mappingName || 'تعيين انضمام'}) إلى طابور المعالجة`);
      }

      // Connection lost event
      tiktokConnection.on('disconnected', (reason) => {
        console.log(`Disconnected from TikTok LIVE. Reason: ${reason}`);
        tiktokConnection = null;
        activeUsername = '';
        io.emit('connectionStatus', { connected: false, username: '' });
      });

      // Socket close event
      tiktokConnection.on('streamEnd', (actionId) => {
        console.log(`Stream ended. Action ID: ${actionId}`);
        if (tiktokConnection) {
          tiktokConnection.disconnect();
          tiktokConnection = null;
          activeUsername = '';
          io.emit('connectionStatus', { connected: false, username: '' });
          io.emit('connectionError', { message: 'Stream ended by host' });
        }
      });

    } catch (err) {
      console.error('Failed to connect to TikTok:', err);
      console.error('Error details:', JSON.stringify(err, null, 2));
      tiktokConnection = null;

      // Send detailed error to client
      let errorMessage = 'Could not connect to TikTok LIVE';

      if (err.message) {
        errorMessage = err.message;
      }

      // Add specific error messages for common issues
      if (errorMessage.includes('Room not found') || errorMessage.includes('User not found')) {
        errorMessage = `المستخدم "${username}" غير موجود أو ليس في وضع البث المباشر حالياً`;
      } else if (errorMessage.includes('connected') || errorMessage.includes('timeout')) {
        errorMessage = 'خطأ في الاتصال بخوادم TikTok، يرجى المحاولة لاحقاً';
      }

      socket.emit('connectionError', { message: errorMessage });
    }
  });

  // Handle disconnect from TikTok Live
  socket.on('disconnectFromTikTok', () => {
    if (tiktokConnection) {
      tiktokConnection.disconnect();
      tiktokConnection = null;
      activeUsername = '';
      io.emit('connectionStatus', { connected: false, username: '' });
      console.log('Disconnected from TikTok LIVE by user request');
    }
  });

  // ===========================
  // نظام محاكاة الأحداث للاختبار
  // ===========================

  // معالج محاكاة الأحداث المختلفة
  socket.on('simulateEvent', (data) => {
    try {
      const { eventType, eventData } = data;
      console.log(`🧪 تم استقبال طلب محاكاة حدث ${eventType}:`, eventData);

      // تحميل بيانات الملف الشخصي النشط
      loadProfileData(activeProfile);

      switch (eventType) {
        case 'gift':
          simulateGiftEvent(eventData);
          break;
        case 'comment':
          simulateCommentEvent(eventData);
          break;
        case 'like':
          simulateLikeEvent(eventData);
          break;
        case 'follow':
          simulateFollowEvent(eventData);
          break;
        case 'share':
          simulateShareEvent(eventData);
          break;
        case 'join':
          simulateJoinEvent(eventData);
          break;
        default:
          socket.emit('errorMessage', { message: `نوع الحدث غير مدعوم: ${eventType}` });
          return;
      }

      socket.emit('eventSimulated', {
        success: true,
        eventType: eventType,
        message: `تم محاكاة حدث ${eventType} بنجاح`
      });

    } catch (error) {
      console.error('خطأ في محاكاة الحدث:', error);
      socket.emit('eventSimulated', {
        success: false,
        error: error.message
      });
    }
  });

  // دوال محاكاة الأحداث (داخل نطاق socket connection)
  function simulateGiftEvent(eventData) {
    const { giftId, giftName, nickname, uniqueId, repeatCount, diamondCount } = eventData;

    // إنشاء بيانات هدية محاكاة
    const giftData = {
      uniqueId: uniqueId || 'test_user',
      nickname: nickname || 'مستخدم تجريبي',
      giftId: giftId || 'Rose',
      giftName: giftName || 'Rose',
      repeatCount: repeatCount || 1,
      diamondCount: diamondCount || 1,
      repeatEnd: true // مهم لمعالجة الهدايا بشكل صحيح
    };

    // البحث عن معلومات الهدية من قاعدة البيانات
    const giftInfo = tikTokGifts.find(g => g.id === giftData.giftId || g.name === giftData.giftName);
    if (giftInfo) {
      giftData.imageUrl = giftInfo.imageUrl;
      giftData.diamondCount = giftInfo.diamondCount || giftData.diamondCount;
    }

    console.log(`🧪 محاكاة هدية: ${giftData.giftName} من ${giftData.nickname}`);

    // تشغيل نفس منطق معالجة الهدايا الحقيقية
    try {
      let mapping = null;

      // البحث عن تعيين للهدية
      if (giftData.giftId && giftMappings && Array.isArray(giftMappings) && giftMappings.length > 0) {
        mapping = giftMappings.find(m => String(m.giftId) === String(giftData.giftId));
      }
      if (!mapping && giftData.giftName && giftMappings && Array.isArray(giftMappings) && giftMappings.length > 0) {
        mapping = giftMappings.find(m =>
          m.giftName &&
          m.giftName.trim().toLowerCase() === giftData.giftName.trim().toLowerCase()
        );
      }
      if (!mapping && giftMappings && Array.isArray(giftMappings) && giftMappings.length > 0) {
        mapping = giftMappings.find(m => m.giftId === 'any');
      }

      if (mapping) {
        console.log(`🧪 تم العثور على تعيين للهدية المحاكاة: ${giftData.giftName}`);

        // استخدام نفس نظام الطوابير الحقيقي
        if (tiktokConnection) {
          // إذا كان هناك اتصال TikTok، استخدم النظام الحقيقي
          const queueKey = `gift_${giftData.uniqueId}_${giftData.giftId}_simulated`;
          const eventDuration = (mapping.duration || 5) * 1000; // تحويل الثواني إلى ملي ثانية
          const pushToQueue = createEventQueue(queueKey, eventDuration);

          const repeatTimes = giftData.repeatCount || 1;
          console.log(`🧪 سيتم تنفيذ الإجراء ${repeatTimes} مرة للهدية المحاكاة ${giftData.giftName}`);

          for (let i = 0; i < repeatTimes; i++) {
            pushToQueue({
              type: 'gift',
              data: { ...giftData, repeatCount: 1 },
              mapping: mapping
            });
          }
        } else {
          // إذا لم يكن هناك اتصال، تنفيذ مباشر
          executeSimulatedAction('gift', giftData, mapping, giftData.repeatCount || 1);
        }
      } else {
        console.log(`🧪 لم يتم العثور على تعيين للهدية المحاكاة: ${giftData.giftName}`);
      }
    } catch (err) {
      console.error('خطأ في تنفيذ إجراء الهدية المحاكاة:', err);
    }

    // إرسال الحدث للغرف المناسبة بناءً على التعيينات (كحدث محاكاة)
    sendEventToAppropriateOverlays('simulatedGift', giftData);
  }

  function simulateJoinEvent(eventData) {
    const { nickname, uniqueId } = eventData;

    const joinData = {
      uniqueId: uniqueId || 'test_user',
      nickname: nickname || 'مستخدم تجريبي'
    };

    console.log(`🧪 محاكاة انضمام من ${joinData.nickname}`);

    // البحث عن تعيين للانضمام
    try {
      let mapping = null;

      if (giftMappings && Array.isArray(giftMappings) && giftMappings.length > 0) {
        mapping = giftMappings.find(m => m.eventType === 'join');
      }

      if (mapping) {
        console.log(`🧪 تم العثور على تعيين للانضمام المحاكاة`);
        executeSimulatedAction('join', joinData, mapping, 1);
      } else {
        console.log(`🧪 لم يتم العثور على تعيين للانضمام المحاكاة`);
      }
    } catch (err) {
      console.error('خطأ في تنفيذ إجراء الانضمام المحاكي:', err);
    }

    // إرسال الحدث للغرف المناسبة بناءً على التعيينات (كحدث محاكاة)
    sendEventToAppropriateOverlays('simulatedJoin', joinData);
  }

  function simulateCommentEvent(eventData) {
    const { comment, nickname, uniqueId } = eventData;

    const commentData = {
      uniqueId: uniqueId || 'test_user',
      nickname: nickname || 'مستخدم تجريبي',
      comment: comment || 'تعليق تجريبي'
    };

    console.log(`🧪 محاكاة تعليق: "${commentData.comment}" من ${commentData.nickname}`);

    // البحث عن تعيين للتعليقات
    try {
      let mapping = null;

      if (giftMappings && Array.isArray(giftMappings) && giftMappings.length > 0) {
        mapping = giftMappings.find(m => m.eventType === 'comment');
      }

      if (mapping) {
        console.log(`🧪 تم العثور على تعيين للتعليقات المحاكاة`);
        executeSimulatedAction('comment', commentData, mapping, 1);
      } else {
        console.log(`🧪 لم يتم العثور على تعيين للتعليقات المحاكاة`);
      }
    } catch (err) {
      console.error('خطأ في تنفيذ إجراء التعليق المحاكي:', err);
    }

    // إرسال الحدث للغرف المناسبة بناءً على التعيينات (كحدث محاكاة)
    sendEventToAppropriateOverlays('simulatedComment', commentData);
  }

  function simulateLikeEvent(eventData) {
    const { likeCount, nickname, uniqueId, totalLikeCount } = eventData;

    const likeData = {
      uniqueId: uniqueId || 'test_user',
      nickname: nickname || 'مستخدم تجريبي',
      likeCount: likeCount || 1,
      totalLikeCount: totalLikeCount || likeCount || 1
    };

    console.log(`🧪 محاكاة إعجاب: ${likeData.likeCount} من ${likeData.nickname}`);

    // البحث عن تعيين للإعجابات
    try {
      let mapping = null;

      if (giftMappings && Array.isArray(giftMappings) && giftMappings.length > 0) {
        mapping = giftMappings.find(m => m.eventType === 'like');
      }

      if (mapping) {
        console.log(`🧪 تم العثور على تعيين للإعجابات المحاكاة`);
        executeSimulatedAction('like', likeData, mapping, 1);
      } else {
        console.log(`🧪 لم يتم العثور على تعيين للإعجابات المحاكاة`);
      }
    } catch (err) {
      console.error('خطأ في تنفيذ إجراء الإعجاب المحاكي:', err);
    }

    // إرسال الحدث للغرف المناسبة بناءً على التعيينات (كحدث محاكاة)
    sendEventToAppropriateOverlays('simulatedLike', likeData);
  }

  function simulateFollowEvent(eventData) {
    const { nickname, uniqueId } = eventData;

    const followData = {
      uniqueId: uniqueId || 'test_user',
      nickname: nickname || 'مستخدم تجريبي'
    };

    console.log(`🧪 محاكاة متابعة من ${followData.nickname}`);

    // البحث عن تعيين للمتابعة
    try {
      let mapping = null;

      if (giftMappings && Array.isArray(giftMappings) && giftMappings.length > 0) {
        mapping = giftMappings.find(m => m.eventType === 'follow');
      }

      if (mapping) {
        console.log(`🧪 تم العثور على تعيين للمتابعة المحاكاة`);
        executeSimulatedAction('follow', followData, mapping, 1);
      } else {
        console.log(`🧪 لم يتم العثور على تعيين للمتابعة المحاكاة`);
      }
    } catch (err) {
      console.error('خطأ في تنفيذ إجراء المتابعة المحاكي:', err);
    }

    // إرسال الحدث للغرف المناسبة بناءً على التعيينات (كحدث محاكاة)
    sendEventToAppropriateOverlays('simulatedFollow', followData);
  }

  function simulateShareEvent(eventData) {
    const { nickname, uniqueId } = eventData;

    const shareData = {
      uniqueId: uniqueId || 'test_user',
      nickname: nickname || 'مستخدم تجريبي'
    };

    console.log(`🧪 محاكاة مشاركة من ${shareData.nickname}`);

    // البحث عن تعيين للمشاركة
    try {
      let mapping = null;

      if (giftMappings && Array.isArray(giftMappings) && giftMappings.length > 0) {
        mapping = giftMappings.find(m => m.eventType === 'share');
      }

      if (mapping) {
        console.log(`🧪 تم العثور على تعيين للمشاركة المحاكاة`);
        executeSimulatedAction('share', shareData, mapping, 1);
      } else {
        console.log(`🧪 لم يتم العثور على تعيين للمشاركة المحاكاة`);
      }
    } catch (err) {
      console.error('خطأ في تنفيذ إجراء المشاركة المحاكي:', err);
    }

    // إرسال الحدث للغرف المناسبة بناءً على التعيينات (كحدث محاكاة)
    sendEventToAppropriateOverlays('simulatedShare', shareData);
  }

  async function executeSimulatedAction(type, data, mapping, repeatCount = 1) {
    try {
      console.log(`🧪 تنفيذ إجراء محاكاة ${type} من ${data.nickname || data.uniqueId} (${repeatCount} مرة)`);

      for (let i = 0; i < repeatCount; i++) {
        // إنشاء مصفوفة للعمليات المتوازية في المحاكاة
        const promises = [];

        // تنفيذ محاكاة ضغط المفاتيح إذا كان موجودًا (بدون انتظار)
        if (mapping && mapping.keypressSequence && Array.isArray(mapping.keypressSequence) && mapping.keypressSequence.length > 0) {
          console.log(`🧪 تنفيذ تسلسل مفاتيح من تعيين محاكاة ${type}`);
          promises.push(simulateKeyPressSequence(mapping.keypressSequence));
        }

        // تشغيل الصوت على الخادم إذا كان الخيار مفعلاً (بدون انتظار)
        if (mapping && mapping.playSoundOnServer && mapping.actions && mapping.actions.includes('sound') && mapping.soundFilePath) {
          console.log(`🧪 تشغيل صوت على الخادم للحدث المحاكي ${type}: ${data.nickname || data.uniqueId}`);
          promises.push(playSound(mapping.soundFilePath));
        }

        // تحديد ما إذا كان يجب إرسال الإجراء إلى شاشة العرض (فوراً - بدون انتظار)
        if (mapping) {
          const hasVisualElements = mapping.actions && (mapping.actions.includes('image') || mapping.actions.includes('video'));
          const needsOverlayForSound = mapping.actions && mapping.actions.includes('sound') && !mapping.playSoundOnServer;
          const hasCustomText = mapping.actions && mapping.actions.includes('text') && mapping.customText;
          const hasUserDisplay = mapping.actions && mapping.actions.includes('user-display');

          if (hasVisualElements || needsOverlayForSound || hasCustomText || hasUserDisplay) {
            // إرسال بيانات الإجراء إلى شاشة العرض فوراً
            const actionData = {
              type: mapping.actions && mapping.actions.includes('sound') ? 'sound' :
                (mapping.actions && mapping.actions.includes('image') ? 'image' :
                  (mapping.actions && mapping.actions.includes('video') ? 'video' : 'alert')),
              eventType: type,
              nickname: data.nickname,
              uniqueId: data.uniqueId,
              profilePictureUrl: data.profilePictureUrl || null, // إضافة صورة المستخدم
              actions: mapping.actions || [],
              duration: mapping.duration || 5,
              overlayId: mapping.overlayId || 'default',
              mediaFilePath: mapping.mediaFilePath,
              soundFilePath: mapping.soundFilePath,
              message: getSimulationEventMessage(type, data),
              playSoundOnServer: mapping.playSoundOnServer,
              customText: mapping.customText || '',
              userDisplaySettings: mapping.userDisplaySettings || null // إضافة إعدادات عرض صورة المستخدم
            };

            // إضافة بيانات إضافية حسب نوع الحدث
            if (type === 'gift') {
              actionData.giftId = data.giftId;
              actionData.giftName = data.giftName;
              actionData.repeatCount = data.repeatCount;
              actionData.diamondCount = data.diamondCount;
            } else if (type === 'like') {
              actionData.likeCount = data.likeCount;
              actionData.totalLikeCount = data.totalLikeCount;
            } else if (type === 'comment') {
              actionData.comment = data.comment;
            }

            // إرسال البيانات إلى شاشة العرض المحددة أو جميع شاشات العرض (فوراً)
            if (mapping.overlayId && mapping.overlayId !== 'default') {
              io.to(`overlay-${mapping.overlayId}`).emit('eventAction', actionData);
            } else {
              io.emit('eventAction', actionData);
            }

            console.log(`🧪 تم إرسال إجراء محاكاة ${type} إلى شاشة العرض فوراً`);
          }
        }

        // انتظار انتهاء جميع العمليات المتوازية (الضغطات والأصوات) في المحاكاة
        await Promise.all(promises);

        // تأخير بين التكرارات
        if (i < repeatCount - 1) {
          await new Promise(resolve => setTimeout(resolve, 400));
        }
      }
    } catch (err) {
      console.error(`خطأ في تنفيذ الإجراء المحاكي ${type}:`, err);
    }
  }

  function getSimulationEventMessage(type, data) {
    switch (type) {
      case 'gift':
        return `🧪 ${data.nickname || data.uniqueId} أرسل هدية ${data.giftName} (اختبار)`;
      case 'like':
        return `🧪 ${data.nickname || data.uniqueId} وصل إلى ${data.totalLikeCount || data.likeCount} إعجاب (اختبار)`;
      case 'follow':
        return `🧪 ${data.nickname || data.uniqueId} تابع البث (اختبار)`;
      case 'share':
        return `🧪 ${data.nickname || data.uniqueId} شارك البث (اختبار)`;
      case 'join':
        return `🧪 ${data.nickname || data.uniqueId} انضم إلى البث (اختبار)`;
      case 'comment':
        return `🧪 ${data.nickname || data.uniqueId}: ${data.comment} (اختبار)`;
      default:
        return `🧪 ${data.nickname || data.uniqueId} أرسل حدث ${type} (اختبار)`;
    }
  }





  // معالجة التعليقات المحاكية للاختبار
  socket.on('simulateComment', (data) => {
    console.log('🧪 تعليق محاكي:', data);

    // إرسال التعليق للعملاء
    const commentData = {
      uniqueId: data.uniqueId || 'test_user',
      nickname: data.nickname || 'مستخدم_تجريبي',
      comment: data.comment
    };

    io.emit('comment', commentData);

    // معالجة أوامر الانضمام للفرق
    handleTeamJoinCommands(commentData, io);
  });

  // معالج تسجيل userId في session
  socket.on('userAuthenticated', async (userData) => {
    console.log('🔐 Received userAuthenticated:', userData);
    if (userData && userData.userId) {
      const session = userSessions.get(socket.id);
      if (session) {
        session.userId = userData.userId;
        session.email = userData.email;
        console.log(`✅ User session registered: ${userData.email} (Socket: ${socket.id})`);
        console.log(`📊 Total sessions: ${userSessions.size}`);

        // تحديث المستخدم الحالي وفحص الاشتراك
        subscriptionStatus.currentUserId = userData.userId;
        console.log('🔍 فحص الاشتراك عند تسجيل الدخول...');
        subscriptionStatus.hasSubscription = await checkSubscriptionFromServer(userData.userId);
        subscriptionStatus.lastChecked = Date.now();
        
        // تحديد التعيينات المسموحة مرة واحدة
        if (!subscriptionStatus.hasSubscription) {
          subscriptionStatus.allowedMappings = giftMappings.slice(0, 5).map(m => m.id);
          console.log(`✅ مستخدم مجاني - مسموح بـ 5 تعيينات فقط`);
        } else {
          subscriptionStatus.allowedMappings = null; // مشترك = كل شيء مسموح
          console.log(`✅ مستخدم مشترك - جميع التعيينات مسموحة`);
        }
      } else {
        console.log(`❌ No session found for socket: ${socket.id}`);
      }
    } else {
      console.log('❌ Invalid userData received');
    }
  });

  // إرسال حالة الاشتراك
  socket.on('getSubscriptionStatus', () => {
    socket.emit('subscriptionStatus', {
      hasSubscription: subscriptionStatus.hasSubscription
    });
  });

  // Handle client disconnect
  socket.on('disconnect', () => {
    console.log('User disconnected');
    // إزالة session
    userSessions.delete(socket.id);
  });

  // معالجة استعلام إعدادات القراءة الصوتية
  socket.on('getTtsSettings', () => {
    socket.emit('ttsSettings', ttsSettings);
  });

  // معالجة تحديث إعداد معين للقراءة الصوتية
  socket.on('updateTtsSetting', async (data) => {
    // التحقق من قيود الأصوات للمستخدمين غير المشتركين
    if (data.voice && data.voice !== 'ar-EG-ShakirNeural') {
      // حماية من الخادم - التحقق من الاشتراك
      const userId = getUserIdFromSocket(socket);
      if (userId) {
        const hasSubscription = await checkSubscriptionFromServer(userId);
        if (!hasSubscription) {
          socket.emit('errorMessage', {
            message: 'النسخة المجانية تسمح باستخدام صوت "شاكر" فقط. يرجى الاشتراك للوصول إلى جميع الأصوات.'
          });
          return;
        }
      } else {
        socket.emit('errorMessage', {
          message: 'يرجى تسجيل الدخول لاستخدام الأصوات المتقدمة.'
        });
        return;
      }
    }

    // تحديث الإعداد المحدد باستخدام وظيفة updateTtsSettings من ملف speakText-edge-tts.js
    const { updateTtsSettings } = require('./speakText-edge-tts.js');
    updateTtsSettings(data);

    // لا نحفظ البروفايل مع كل تغيير صغير، فقط نرسل الإعدادات المحدثة
    // سيتم حفظ الإعدادات عند النقر على زر "حفظ الإعدادات"

    // إرسال الإعدادات المحدثة لجميع العملاء
    io.emit('ttsSettings', ttsSettings);
  });

  // معالجة حفظ إعدادات القراءة الصوتية
  socket.on('saveTtsSettings', async (data) => {
    if (data) {
      // حماية صارمة للأصوات المتقدمة
      if (data.voice && data.voice !== 'ar-EG-ShakirNeural') {
        const userId = getUserIdFromSocket(socket);
        if (!userId) {
          socket.emit('errorMessage', {
            message: 'يرجى تسجيل الدخول لاستخدام الأصوات المتقدمة.'
          });
          return;
        }

        // تحقق فوري من الاشتراك
        const hasSubscription = await checkSubscriptionFromServer(userId);
        if (!hasSubscription) {
          // فرض صوت شاكر - لا يمكن تجاوزه
          data.voice = 'ar-EG-ShakirNeural';
          console.log(`🔒 Forced free/expired user to default voice: ${userId}`);
          socket.emit('errorMessage', {
            message: 'انتهت صلاحية اشتراكك أو أنت مستخدم مجاني. تم تغيير الصوت إلى "شاكر".'
          });
        }
      }

      // تحديث الإعدادات باستخدام وظيفة updateTtsSettings من ملف speakText-edge-tts.js
      const { updateTtsSettings } = require('./speakText-edge-tts.js');
      updateTtsSettings(data);

      // حفظ الإعدادات في ملف البروفايل النشط
      saveProfileData(activeProfile);

      // إرسال رسالة نجاح
      socket.emit('ttsSettingsSaved');

      // إرسال الإعدادات المحدثة لجميع العملاء
      io.emit('ttsSettings', ttsSettings);
    }
  });

  // معالجة طلب اختبار الصوت
  socket.on('testTtsVoice', async (data) => {
    try {
      if (data && data.text) {
        // التحقق من قيود الأصوات للمستخدمين غير المشتركين
        const testModel = data.model || ttsSettings.voice;
        if (testModel && testModel !== 'ar-EG-ShakirNeural') {
          // حماية من الخادم - التحقق من الاشتراك
          const userId = getUserIdFromSocket(socket);
          if (userId) {
            const hasSubscription = await checkSubscriptionFromServer(userId);
            if (!hasSubscription) {
              socket.emit('testTtsVoiceResult', {
                success: false,
                error: 'النسخة المجانية تسمح باستخدام صوت "شاكر" فقط. يرجى الاشتراك للوصول إلى جميع الأصوات.'
              });
              return;
            }
          } else {
            socket.emit('testTtsVoiceResult', {
              success: false,
              error: 'يرجى تسجيل الدخول لاستخدام الأصوات المتقدمة.'
            });
            return;
          }
        }

        // استخدام البيانات المتوفرة أو الإعدادات الافتراضية
        const testSpeed = parseFloat(data.speed) || ttsSettings.speed;
        const testVolume = parseInt(data.volume) || ttsSettings.volume;

        console.log(`اختبار الصوت: النموذج=${testModel}, السرعة=${testSpeed}, مستوى الصوت=${testVolume}`);

        // احفظ الإعدادات الحالية
        const originalModel = ttsSettings.voice;
        const originalSpeed = ttsSettings.speed;
        const originalVolume = ttsSettings.volume;

        // استخدم الإعدادات المؤقتة للاختبار
        const { updateTtsSettings } = require('./speakText-edge-tts.js');
        updateTtsSettings({
          voice: testModel,
          speed: testSpeed,
          volume: testVolume
        });

        // قراءة النص
        await new Promise((resolve) => {
          speakText(data.text, () => {
            // استعادة الإعدادات الأصلية بعد انتهاء التشغيل
            updateTtsSettings({
              voice: originalModel,
              speed: originalSpeed,
              volume: originalVolume
            });
            resolve();
          });
        });

        // إرسال نتيجة النجاح
        socket.emit('testTtsVoiceResult', { success: true });
      } else {
        throw new Error('البيانات المطلوبة غير متوفرة');
      }
    } catch (error) {
      console.error('خطأ في اختبار الصوت:', error);
      socket.emit('testTtsVoiceResult', { success: false, error: error.message });
    }
  });

  // Handle get display settings
  socket.on('getDisplaySettings', () => {
    socket.emit('displaySettingsUpdated', { settings: displaySettings });
  });

  // Handle update display settings
  socket.on('updateDisplaySettings', (data) => {
    if (data.settings) {
      console.log('Recibiendo actualización de configuración de overlay:', data.settings);

      // Crear una copia profunda de las configuraciones actuales
      const currentSettings = JSON.parse(JSON.stringify(displaySettings));

      // Verificar y procesar cada propiedad individualmente
      const newSettings = data.settings;

      // Propiedades básicas
      currentSettings.showUsernames = newSettings.showUsernames !== undefined ? newSettings.showUsernames : currentSettings.showUsernames;
      currentSettings.showGiftNames = newSettings.showGiftNames !== undefined ? newSettings.showGiftNames : currentSettings.showGiftNames;
      currentSettings.showGiftImages = newSettings.showGiftImages !== undefined ? newSettings.showGiftImages : currentSettings.showGiftImages;
      currentSettings.showDiamondCount = newSettings.showDiamondCount !== undefined ? newSettings.showDiamondCount : currentSettings.showDiamondCount;
      currentSettings.alertPosition = newSettings.alertPosition || currentSettings.alertPosition;
      currentSettings.alertTheme = newSettings.alertTheme || currentSettings.alertTheme;
      currentSettings.alertSize = newSettings.alertSize || currentSettings.alertSize;
      currentSettings.alertDuration = newSettings.alertDuration !== undefined ? newSettings.alertDuration : currentSettings.alertDuration;
      currentSettings.showBigGifts = newSettings.showBigGifts !== undefined ? newSettings.showBigGifts : currentSettings.showBigGifts;
      currentSettings.showFollows = newSettings.showFollows !== undefined ? newSettings.showFollows : currentSettings.showFollows;
      currentSettings.showLikes = newSettings.showLikes !== undefined ? newSettings.showLikes : currentSettings.showLikes;
      currentSettings.showComments = newSettings.showComments !== undefined ? newSettings.showComments : currentSettings.showComments;

      // Propiedades de estilo
      currentSettings.backgroundColor = newSettings.backgroundColor || currentSettings.backgroundColor;
      currentSettings.textColor = newSettings.textColor || currentSettings.textColor;
      currentSettings.accentColor = newSettings.accentColor || currentSettings.accentColor;
      currentSettings.fontFamily = newSettings.fontFamily || currentSettings.fontFamily;

      // Propiedades numéricas (asegurarse de que sean números)
      currentSettings.fontSize = typeof newSettings.fontSize === 'number' ? newSettings.fontSize :
        (newSettings.fontSize ? parseInt(newSettings.fontSize, 10) : currentSettings.fontSize);

      currentSettings.width = typeof newSettings.width === 'number' ? newSettings.width :
        (newSettings.width ? parseInt(newSettings.width, 10) : currentSettings.width);

      currentSettings.opacity = typeof newSettings.opacity === 'number' ? newSettings.opacity :
        (newSettings.opacity ? parseInt(newSettings.opacity, 10) : currentSettings.opacity);

      currentSettings.backgroundOpacity = typeof newSettings.backgroundOpacity === 'number' ? newSettings.backgroundOpacity :
        (newSettings.backgroundOpacity ? parseInt(newSettings.backgroundOpacity, 10) : currentSettings.backgroundOpacity);

      currentSettings.maxItems = typeof newSettings.maxItems === 'number' ? newSettings.maxItems :
        (newSettings.maxItems ? parseInt(newSettings.maxItems, 10) : currentSettings.maxItems);

      // Propiedades booleanas
      currentSettings.enableAnimations = newSettings.enableAnimations !== undefined ? newSettings.enableAnimations : currentSettings.enableAnimations;
      currentSettings.loopAnimation = newSettings.loopAnimation !== undefined ? newSettings.loopAnimation : currentSettings.loopAnimation;

      // Propiedades de texto
      currentSettings.animationType = newSettings.animationType || currentSettings.animationType;

      // Objetos anidados
      if (newSettings.customPosition) {
        currentSettings.customPosition = {
          x: typeof newSettings.customPosition.x === 'number' ? newSettings.customPosition.x :
            (newSettings.customPosition.x ? parseInt(newSettings.customPosition.x, 10) : currentSettings.customPosition.x),
          y: typeof newSettings.customPosition.y === 'number' ? newSettings.customPosition.y :
            (newSettings.customPosition.y ? parseInt(newSettings.customPosition.y, 10) : currentSettings.customPosition.y)
        };
      }

      if (newSettings.sound) {
        currentSettings.sound = {
          enabled: newSettings.sound.enabled !== undefined ? newSettings.sound.enabled : currentSettings.sound.enabled,
          volume: typeof newSettings.sound.volume === 'number' ? newSettings.sound.volume :
            (newSettings.sound.volume ? parseInt(newSettings.sound.volume, 10) : currentSettings.sound.volume)
        };
      }

      // Actualizar las configuraciones globales
      displaySettings = currentSettings;

      console.log('Configuración actualizada:', displaySettings);
      console.log('Opacidad de fondo guardada:', displaySettings.backgroundOpacity);

      // Guardar los datos en el perfil activo
      saveProfileData(activeProfile);

      // Notificar a todos los clientes sobre la actualización
      io.emit('displaySettingsUpdated', { settings: displaySettings });

      // Notificar específicamente a las ventanas de overlay
      io.emit('overlaySettingsUpdated', { settings: displaySettings });

      console.log('Configuración de overlay actualizada y guardada correctamente');
    }
  });

  // Handle save gift mapping
  socket.on('saveGiftMapping', async (data) => {
    // حماية صارمة من الخادم - تحقق فوري من Firebase
    const userId = getUserIdFromSocket(socket);
    if (!userId) {
      socket.emit('errorMessage', {
        message: 'يرجى تسجيل الدخول للوصول إلى هذه الميزة.'
      });
      return;
    }

    // تحقق فوري من الاشتراك (لا نعتمد على cache)
    const hasSubscription = await checkSubscriptionFromServer(userId);

    if (!hasSubscription) {
      // فرض حد المجانيين - لا يمكن تجاوزه
      const freeLimit = 5;
      if (giftMappings.length >= freeLimit) {
        socket.emit('errorMessage', {
          message: `انتهت صلاحية اشتراكك أو وصلت للحد المجاني (${freeLimit} ربطات). يرجى الاشتراك للمتابعة.`
        });
        return;
      }
      console.log(`⚠️ Free user saving mapping (${giftMappings.length + 1}/${freeLimit})`);
    } else {
      console.log(`✅ Premium user saving mapping`);
    }

    try {
      console.log('Saving mapping data:', data);

      // التحقق من البيانات حسب نوع الحدث
      if (data.eventType === 'gift' || !data.eventType) {
        // للهدايا، يجب اختيار هدية
        if (!data.giftId) {
          socket.emit('errorMessage', { message: 'يرجى اختيار هدية' });
          return;
        }
      } else if (data.eventType === 'like') {
        // للإعجابات، يجب تحديد عدد الإعجابات
        if (!data.likeCount || data.likeCount < 1) {
          socket.emit('errorMessage', { message: 'يرجى تحديد عدد الإعجابات المطلوب' });
          return;
        }
      } else if (data.eventType === 'comment') {
        // للتعليقات، يجب إدخال نص التعليق
        if (!data.commentText) {
          socket.emit('errorMessage', { message: 'يرجى إدخال نص التعليق' });
          return;
        }
      } else if (data.eventType === 'join' && data.joinType === 'specific') {
        // للانضمام المحدد، يجب إدخال اسم المستخدم
        if (!data.specificUsername) {
          socket.emit('errorMessage', { message: 'يرجى إدخال اسم المستخدم المحدد' });
          return;
        }
      }

      // التحقق من قيمة الشرط فقط للهدايا وعندما يكون الشرط ليس "أي"
      if (data.eventType === 'gift' && data.condition !== 'any' && !data.conditionValue) {
        socket.emit('errorMessage', { message: 'يرجى إدخال قيمة للشرط' });
        return;
      }

      // التحقق من وجود ملف صوتي إذا كان الإجراء يتضمن صوت
      if (data.actions && data.actions.includes('sound') && !data.soundFilePath && !data.soundFile) {
        socket.emit('errorMessage', { message: 'يرجى اختيار ملف صوتي' });
        return;
      }

      // التحقق من وجود ملف وسائط إذا كان الإجراء يتضمن صورة أو فيديو
      if (data.actions && (data.actions.includes('image') || data.actions.includes('video')) && !data.mediaFilePath && !data.filePath) {
        socket.emit('errorMessage', { message: 'يرجى اختيار ملف وسائط' });
        return;
      }

      // التحقق من وجود تسلسل مفاتيح إذا كان الإجراء هو محاكاة مفاتيح
      if (data.actions && data.actions.includes('keypress')) {
        // إذا لم يكن هناك تسلسل مفاتيح ولم يكن هناك معلومات مفتاح، أضف تسلسل فارغ
        if (!data.keypressSequence && !data.keypressInfo) {
          data.keypressSequence = [];
        }

        // إذا كان هناك معلومات مفتاح ولكن لا يوجد تسلسل، قم بتحويل معلومات المفتاح إلى تسلسل
        if (data.keypressInfo && (!data.keypressSequence || !Array.isArray(data.keypressSequence) || data.keypressSequence.length === 0)) {
          data.keypressSequence = [data.keypressInfo];
        }
      }

      // إذا كان له معرف، فهو تحديث لتعيين موجود
      if (data.id) {
        // تحويل المعرفات إلى نصوص للمقارنة
        const index = giftMappings.findIndex(m => String(m.id) === String(data.id));
        if (index !== -1) {
          // الاحتفاظ بالمعرف الأصلي
          const originalId = giftMappings[index].id;
          // دمج البيانات الجديدة مع الاحتفاظ بالمعرف الأصلي
          giftMappings[index] = { ...data, id: originalId };
          console.log('تم تحديث التعيين برقم:', originalId);
        } else {
          // إذا لم يتم العثور على التعيين، أنشئ تعيين جديد بدلاً من إرسال خطأ
          console.log('لم يتم العثور على التعيين للتحديث، إنشاء تعيين جديد بدلاً من ذلك:', data.id);
          data.id = Date.now().toString(); // تعيين معرف جديد
          giftMappings.push(data);
          console.log('تم إنشاء تعيين جديد برقم:', data.id);
        }
      } else {
        // تعيين جديد
        data.id = Date.now().toString();
        giftMappings.push(data);
        console.log('تم إنشاء تعيين جديد برقم:', data.id);
      }

      socket.emit('mappingSaved', { message: 'تم حفظ التعيين بنجاح' });
      io.emit('giftMappingsUpdated', { mappings: giftMappings });
      // حفظ التعيينات فعليًا في ملف البروفايل النشط
      saveProfileData(activeProfile);
    } catch (error) {
      console.error('Error saving gift mapping:', error);
      socket.emit('errorMessage', { message: 'حدث خطأ أثناء حفظ التعيين' });
    }
  });

  // Handle delete gift mapping
  socket.on('deleteGiftMapping', async (data) => {
    // حماية من الخادم - التحقق من الاشتراك
    const userId = getUserIdFromSocket(socket);
    if (userId) {
      const hasSubscription = await checkSubscriptionFromServer(userId);
      if (!hasSubscription) {
        // المستخدمون المجانيون يمكنهم حذف ربطاتهم المجانية
        console.log(`⚠️ Free user deleting mapping`);
        // السماح بالحذف للمستخدمين المجانيين
      }
    } else {
      socket.emit('errorMessage', {
        message: 'يرجى تسجيل الدخول للوصول إلى هذه الميزة.'
      });
      return;
    }

    try {
      if (!data.id) {
        socket.emit('errorMessage', { message: 'معرف التعيين غير صالح' });
        return;
      }

      // تحويل المعرفات إلى نصوص للمقارنة
      const index = giftMappings.findIndex(m => String(m.id) === String(data.id));
      if (index !== -1) {
        const deletedId = giftMappings[index].id;
        giftMappings.splice(index, 1);
        console.log('تم حذف التعيين برقم:', deletedId);
        socket.emit('mappingDeleted', { message: 'تم حذف التعيين بنجاح', id: deletedId });
        io.emit('giftMappingsUpdated', { mappings: giftMappings });
        // حفظ التعيينات فعليًا في ملف البروفايل النشط بعد الحذف
        saveProfileData(activeProfile);
      } else {
        console.log('لم يتم العثور على التعيين للحذف:', data.id);
        socket.emit('errorMessage', { message: 'التعيين غير موجود' });
      }
    } catch (error) {
      console.error('Error deleting gift mapping:', error);
      socket.emit('errorMessage', { message: 'حدث خطأ أثناء حذف التعيين' });
    }
  });

  // Handle update available gifts
  socket.on('updateAvailableGifts', async () => {
    try {
      const newGifts = await fetchGiftsList();
      tikTokGifts = newGifts;
      socket.emit('giftsUpdated', { message: 'تم تحديث قائمة الهدايا بنجاح', count: newGifts.length });
      io.emit('availableGifts', { gifts: tikTokGifts });
    } catch (error) {
      console.error('Error updating gifts list:', error);
      socket.emit('errorMessage', { message: 'حدث خطأ أثناء تحديث قائمة الهدايا' });
    }
  });

  // Handle settings
  socket.on('getSettings', () => {
    loadProfileData(activeProfile);
    socket.emit('settings', displaySettings);
  });

  // Handle contact form submission
  socket.on('submitContactForm', (formData) => {
    console.log('Contact form submission received:', formData);
    // هنا يمكنك معالجة بيانات نموذج الاتصال، مثل إرسالها بالبريد الإلكتروني أو حفظها في قاعدة بيانات
    socket.emit('contactFormSubmitted', { success: true, message: 'تم استلام رسالتك بنجاح' });
  });

  // Profile management
  socket.on('getProfiles', () => {
    socket.emit('profilesUpdated', {
      profiles: profiles,
      activeProfile: activeProfile
    });
  });

  socket.on('createProfile', async (data) => {
    try {
      const { name, userId } = data;
      if (!name) {
        socket.emit('errorMessage', { message: 'اسم الملف الشخصي مطلوب' });
        return;
      }

      // حماية مزدوجة من الخادم - حد الملفات الشخصية
      const sessionUserId = getUserIdFromSocket(socket);
      if (sessionUserId) {
        const hasSubscription = await checkSubscriptionFromServer(sessionUserId);
        if (!hasSubscription) {
          const nonDefaultProfiles = profiles.filter(profile => profile.id !== 'default');
          const freeLimit = 1;

          if (nonDefaultProfiles.length >= freeLimit) {
            socket.emit('errorMessage', {
              message: `النسخة المجانية تسمح بـ ${freeLimit} ملف شخصي إضافي فقط. لديك ${nonDefaultProfiles.length} ملف. احذف ملف موجود أو اشترك للحصول على ملفات غير محدودة.`
            });
            return;
          }
          console.log(`⚠️ Free user creating profile (${nonDefaultProfiles.length + 1}/${freeLimit})`);
        }
      } else {
        socket.emit('errorMessage', {
          message: 'يرجى تسجيل الدخول لإنشاء ملفات شخصية.'
        });
        return;
      }

      const id = `profile_${Date.now()}`;
      const newProfile = {
        id: id,
        name: name,
        created: Date.now(),
        lastModified: Date.now()
      };
      profiles.push(newProfile);
      saveProfilesList();
      // حفظ ملف البروفايل الجديد بقيم افتراضية فقط
      const defaultProfileData = {
        giftMappings: [],
        displaySettings: {
          showUsernames: true,
          showGiftNames: true,
          showGiftImages: true,
          showDiamondCount: true,
          alertPosition: 'bottom-left',
          alertTheme: 'default',
          alertSize: 'normal',
          alertDuration: 5,
          showBigGifts: true,
          showFollows: false,
          showLikes: false,
          showComments: false,
          backgroundColor: '#000000',
          textColor: '#ffffff',
          accentColor: '#ff3b5c',
          fontFamily: "'Tajawal', sans-serif",
          fontSize: 16,
          enableAnimations: true,
          loopAnimation: false,
          width: 30,
          opacity: 80,
          backgroundOpacity: 80,
          maxItems: 5,
          customPosition: { x: 10, y: 10 },
          sound: { enabled: false, volume: 50 }
        }
      };
      const profileDataPath = path.join(profilesDir, `${id}.json`);
      fs.writeFileSync(profileDataPath, JSON.stringify(defaultProfileData, null, 2), 'utf8');
      socket.emit('profileCreated', newProfile);
      io.emit('profilesUpdated', { profiles: profiles, activeProfile: activeProfile });
    } catch (error) {
      socket.emit('errorMessage', { message: 'حدث خطأ أثناء إنشاء الملف الشخصي' });
    }
  });

  socket.on('updateProfile', (data) => {
    try {
      const { id, name } = data;

      if (!id || !name) {
        socket.emit('errorMessage', { message: 'معرف واسم الملف الشخصي مطلوبان' });
        return;
      }

      // Find profile
      const profileIndex = profiles.findIndex(p => p.id === id);
      if (profileIndex === -1) {
        socket.emit('errorMessage', { message: 'الملف الشخصي غير موجود' });
        return;
      }

      // Update profile
      profiles[profileIndex].name = name;
      profiles[profileIndex].lastModified = Date.now();
      saveProfilesList();

      socket.emit('profileUpdated', profiles[profileIndex]);
      io.emit('profilesUpdated', {
        profiles: profiles,
        activeProfile: activeProfile
      });
    } catch (error) {
      console.error('Error updating profile:', error);
      socket.emit('errorMessage', { message: 'حدث خطأ أثناء تحديث الملف الشخصي' });
    }
  });

  socket.on('deleteProfile', async (data) => {
    try {
      const { id } = data;

      if (!id) {
        socket.emit('errorMessage', { message: 'معرف الملف الشخصي مطلوب' });
        return;
      }

      // Cannot delete default profile
      if (id === 'default') {
        socket.emit('errorMessage', { message: 'لا يمكن حذف الملف الشخصي الافتراضي' });
        return;
      }

      // حماية من الخادم - التحقق من الاشتراك
      const userId = getUserIdFromSocket(socket);
      if (userId) {
        const hasSubscription = await checkSubscriptionFromServer(userId);
        if (!hasSubscription) {
          socket.emit('errorMessage', {
            message: 'حذف الملفات الشخصية يتطلب اشتراك نشط. يرجى الاشتراك للوصول إلى هذه الميزة.'
          });
          return;
        }
      } else {
        socket.emit('errorMessage', {
          message: 'يرجى تسجيل الدخول لحذف الملفات الشخصية.'
        });
        return;
      }

      // Find profile
      const profileIndex = profiles.findIndex(p => p.id === id);
      if (profileIndex === -1) {
        socket.emit('errorMessage', { message: 'الملف الشخصي غير موجود' });
        return;
      }

      // If active profile is deleted, switch to default
      if (id === activeProfile) {
        setActiveProfile('default');
      }

      // Delete profile data file
      const profileDataPath = path.join(profilesDir, `${id}.json`);
      if (fs.existsSync(profileDataPath)) {
        fs.unlinkSync(profileDataPath);
      }

      // Remove from profiles list
      profiles.splice(profileIndex, 1);
      saveProfilesList();

      socket.emit('profileDeleted', { id });
      io.emit('profilesUpdated', {
        profiles: profiles,
        activeProfile: activeProfile
      });
    } catch (error) {
      console.error('Error deleting profile:', error);
      socket.emit('errorMessage', { message: 'حدث خطأ أثناء حذف الملف الشخصي' });
    }
  });

  socket.on('setActiveProfile', async (data) => {
    try {
      const { id } = data;

      if (!id) {
        socket.emit('errorMessage', { message: 'معرف الملف الشخصي مطلوب' });
        return;
      }

      // حماية صارمة - منع تبديل الملفات للمجانيين/المنتهيين
      if (id !== 'default') {
        const sessionUserId = getUserIdFromSocket(socket);
        if (!sessionUserId) {
          socket.emit('errorMessage', {
            message: 'يرجى تسجيل الدخول للتبديل بين الملفات الشخصية.'
          });
          return;
        }

        // تحقق فوري من الاشتراك
        const hasSubscription = await checkSubscriptionFromServer(sessionUserId);
        if (!hasSubscription) {
          // فرض البروفايل الافتراضي - لا يمكن تجاوزه
          console.log(`🔒 Forced free/expired user to default profile: ${sessionUserId}`);
          setActiveProfile('default');
          socket.emit('activeProfileSet', { id: 'default' });
          socket.emit('errorMessage', {
            message: 'انتهت صلاحية اشتراكك أو أنت مستخدم مجاني. تم التبديل للبروفايل الافتراضي.'
          });
          return;
        }
      }

      // Find profile
      const profileIndex = profiles.findIndex(p => p.id === id);
      if (profileIndex === -1) {
        socket.emit('errorMessage', { message: 'الملف الشخصي غير موجود' });
        return;
      }

      // Set active profile
      const success = setActiveProfile(id);

      if (success) {
        // Load settings and mappings from the new profile file
        loadProfileData(id);
        // Send updated settings to all clients
        io.emit('giftMappingsUpdated', { mappings: giftMappings });
        io.emit('displaySettingsUpdated', { settings: displaySettings });
        io.emit('profilesUpdated', {
          profiles: profiles,
          activeProfile: activeProfile
        });

        socket.emit('activeProfileSet', { id });
      } else {
        socket.emit('errorMessage', { message: 'حدث خطأ أثناء تعيين الملف الشخصي النشط' });
      }
    } catch (error) {
      console.error('Error setting active profile:', error);
      socket.emit('errorMessage', { message: 'حدث خطأ أثناء تعيين الملف الشخصي النشط' });
    }
  });

  // Import/Export system
  socket.on('exportSettings', () => {
    try {
      // Save current profile data before exporting
      saveProfileData(activeProfile);

      // Prepare export data
      const exportData = {
        version: "1.0",
        date: Date.now(),
        profiles: profiles,
        activeProfile: activeProfile,
        profilesData: {}
      };

      // Add each profile's data
      for (const profile of profiles) {
        const profileDataPath = path.join(profilesDir, `${profile.id}.json`);
        if (fs.existsSync(profileDataPath)) {
          exportData.profilesData[profile.id] = JSON.parse(fs.readFileSync(profileDataPath, 'utf8'));
        }
      }

      socket.emit('exportData', exportData);
    } catch (error) {
      console.error('Error exporting settings:', error);
      socket.emit('errorMessage', { message: 'حدث خطأ أثناء تصدير الإعدادات' });
    }
  });

  socket.on('importSettings', (data) => {
    try {
      const importData = data;

      // Validate import data
      if (!importData || !importData.version || !importData.profiles || !importData.profilesData) {
        socket.emit('errorMessage', { message: 'بيانات الاستيراد غير صالحة' });
        return;
      }

      // Backup current profiles directory
      const backupDir = path.join(__dirname, 'profiles_backup_' + Date.now());
      fs.mkdirSync(backupDir, { recursive: true });

      // Copy all files
      const files = fs.readdirSync(profilesDir);
      for (const file of files) {
        const sourcePath = path.join(profilesDir, file);
        const destPath = path.join(backupDir, file);
        fs.copyFileSync(sourcePath, destPath);
      }

      // Import profiles list
      profiles = importData.profiles;
      saveProfilesList();

      // Import profile data
      for (const profileId in importData.profilesData) {
        const profileData = importData.profilesData[profileId];
        const profileDataPath = path.join(profilesDir, `${profileId}.json`);
        fs.writeFileSync(profileDataPath, JSON.stringify(profileData, null, 2), 'utf8');
      }

      // Set active profile
      if (importData.activeProfile && profiles.some(p => p.id === importData.activeProfile)) {
        setActiveProfile(importData.activeProfile);
      } else if (profiles.length > 0) {
        setActiveProfile(profiles[0].id);
      }

      // Send updated data to all clients
      io.emit('giftMappingsUpdated', { mappings: giftMappings });
      io.emit('displaySettingsUpdated', { settings: displaySettings });
      io.emit('profilesUpdated', {
        profiles: profiles,
        activeProfile: activeProfile
      });

      socket.emit('importComplete', { success: true });
    } catch (error) {
      console.error('Error importing settings:', error);
      socket.emit('errorMessage', { message: 'حدث خطأ أثناء استيراد الإعدادات: ' + error.message });
    }
  });

  // إضافة endpoint لإرجاع اسم المستخدم الأخير
  socket.on('getLastUsername', () => {
    const lastUsername = loadLastUsername();
    socket.emit('lastUsername', { username: lastUsername });
  });

  // اختبار تسلسل المفاتيح
  socket.on('testKeypress', async (data) => {
    try {
      const { keys } = data;

      if (!Array.isArray(keys) || keys.length === 0) {
        socket.emit('errorMessage', { message: 'لم يتم توفير مفاتيح للاختبار' });
        return;
      }

      console.log('اختبار تسلسل المفاتيح:', keys);

      // Calculate total delay for feedback
      let totalDelay = 0;
      keys.forEach(key => {
        totalDelay += (typeof key.delay === 'number' ? key.delay : 0);
      });

      // محاكاة ضغط المفاتيح واحدًا تلو الآخر مع التأخير
      const success = await simulateKeyPressSequence(keys);

      // إرسال استجابة للعميل
      socket.emit('keypressTestResult', {
        success: success,
        message: `تم بدء اختبار تسلسل المفاتيح (${keys.length} مفتاح، التأخير الكلي: ${totalDelay} مللي ثانية)`,
        keysCount: keys.length,
        totalDelay: totalDelay
      });

      // Send a completion message after the sequence completes
      if (success) {
        setTimeout(() => {
          socket.emit('keypressTestResult', {
            success: true,
            message: 'تم الانتهاء من اختبار تسلسل المفاتيح',
            completed: true
          });
        }, totalDelay + 200); // Add a small buffer to ensure all keys are processed
      }
    } catch (error) {
      console.error('Error testing keypress sequence:', error);
      socket.emit('errorMessage', { message: 'حدث خطأ أثناء اختبار تسلسل المفاتيح: ' + error.message });
    }
  });

  // اختبار تسلسل المفاتيح (للتعيينات)
  socket.on('testKeypressSequence', async (data) => {
    try {
      const { sequence } = data;

      if (!Array.isArray(sequence) || sequence.length === 0) {
        socket.emit('errorMessage', { message: 'لم يتم توفير تسلسل مفاتيح للاختبار' });
        return;
      }

      console.log('🧪 اختبار تسلسل المفاتيح من التعيين:', sequence);

      // Calculate total delay for feedback
      let totalDelay = 0;
      sequence.forEach(key => {
        totalDelay += (typeof key.delay === 'number' ? key.delay : 0);
      });

      // محاكاة ضغط المفاتيح واحدًا تلو الآخر مع التأخير
      const success = await simulateKeyPressSequence(sequence);

      // إرسال استجابة للعميل
      socket.emit('keypressTestResult', {
        success: success,
        message: `🧪 تم بدء اختبار تسلسل المفاتيح من التعيين (${sequence.length} مفتاح، التأخير الكلي: ${totalDelay} مللي ثانية)`,
        keysCount: sequence.length,
        totalDelay: totalDelay
      });

      // Send a completion message after the sequence completes
      if (success) {
        setTimeout(() => {
          socket.emit('keypressTestResult', {
            success: true,
            message: '🧪 تم الانتهاء من اختبار تسلسل المفاتيح من التعيين',
            completed: true
          });
        }, totalDelay + 200); // Add a small buffer to ensure all keys are processed
      }
    } catch (error) {
      console.error('Error testing keypress sequence from mapping:', error);
      socket.emit('errorMessage', { message: 'حدث خطأ أثناء اختبار تسلسل المفاتيح من التعيين: ' + error.message });
    }
  });


});

// دالة لمحاكاة تسلسل ضغط المفاتيح
function simulateKeyPressSequence(keys) {
  console.log('Simulating key press sequence:', keys);

  return new Promise((resolve, reject) => {
    try {
      if (!robotInitialized) {
        console.error('RobotJS not initialized');
        resolve(false);
        return;
      }

      if (!keys || keys.length === 0) {
        resolve(true);
        return;
      }

      // حساب إجمالي الوقت المطلوب
      let totalDelay = 0;
      keys.forEach(keyInfo => {
        const delay = typeof keyInfo.delay === 'number' ? keyInfo.delay : 0;
        totalDelay += delay;
      });

      // Process each key with cumulative delay (التأخير قبل الضغط)
      let cumulativeDelay = 0;
      let previousKey = null;

      keys.forEach((keyInfo, index) => {
        const delay = typeof keyInfo.delay === 'number' ? keyInfo.delay : 0;
        cumulativeDelay += delay; // التأخير قبل الضغط

        // إضافة delay إضافي بين الضغطات المتتالية للمفتاح نفسه (مهم للألعاب)
        const currentKey = keyInfo.isCustom ? keyInfo.key.trim().toLowerCase() : keyInfo.key.toLowerCase();
        if (previousKey && previousKey === currentKey) {
          cumulativeDelay += 75; // إضافة 75ms بين الضغطات المتتالية للمفتاح نفسه
          console.log(`إضافة delay إضافي للمفتاح المتكرر: ${currentKey}`);
        }
        previousKey = currentKey;

        // إضافة delay أساسي صغير بين جميع الضغطات (مهم للألعاب)
        if (index > 0) {
          cumulativeDelay += 25; // 25ms delay أساسي بين كل ضغطة
        }

        // تحديد ما إذا كان هذا المفتاح يستخدم الضغط المستمر
        const isHoldKey = keyInfo.holdKey === true;
        const holdDuration = isHoldKey ? (keyInfo.holdDuration || 1000) : 0;

        setTimeout(() => {
          console.log(`[${index + 1}/${keys.length}] Now pressing key:`, keyInfo);
          if (keyInfo.key) {
            try {
              if (keyInfo.isCustom && keyInfo.key.length > 0) {
                const multiKeys = keyInfo.key.split(/[ ,]+/).filter(k => k.trim().length > 0);
                if (multiKeys.length > 1) {
                  // معالجة مفاتيح متعددة مع delay بينها
                  multiKeys.forEach((k, keyIndex) => {
                    setTimeout(() => {
                      if (isHoldKey) {
                        // ضغط مستمر للمفتاح
                        holdKey(k.trim(), keyInfo.modifiers || {}, holdDuration);
                      } else {
                        // ضغط عادي للمفتاح
                        const result = mapKeyForSim(k.trim());
                        if (result) {
                          console.log('Pressed individual key:', result);
                        }
                      }
                    }, keyIndex * 50); // 50ms delay بين المفاتيح المتعددة
                  });
                } else {
                  // مفتاح مخصص واحد
                  if (isHoldKey) {
                    // ضغط مستمر للمفتاح
                    holdKey(keyInfo.key.trim(), keyInfo.modifiers || {}, holdDuration);
                  } else {
                    // ضغط عادي للمفتاح
                    const result = mapKeyForSim(keyInfo.key.trim(), keyInfo.modifiers || {});
                    if (result) {
                      console.log('Pressed custom key:', result);
                    }
                  }
                }
              } else {
                // مفتاح محدد مسبقاً
                if (isHoldKey) {
                  // ضغط مستمر للمفتاح
                  holdKey(keyInfo.key, keyInfo.modifiers || {}, holdDuration);
                } else {
                  // ضغط عادي للمفتاح
                  const result = mapKeyForSim(keyInfo.key, keyInfo.modifiers || {});
                  if (result) {
                    console.log('Pressed predefined key:', result);
                  }
                }
              }
            } catch (err) {
              console.error('Error simulating key press:', err);
            }
          }
        }, cumulativeDelay);
      });

      // انتظار انتهاء جميع المفاتيح
      setTimeout(() => {
        resolve(true);
      }, totalDelay + 100); // إضافة 100ms كهامش أمان

    } catch (err) {
      console.error('Error in key press simulation:', err);
      reject(err);
    }
  });
}

// دالة للضغط المستمر على المفتاح لمدة محددة
function holdKey(key, modifiers = {}, duration = 1000) {
  console.log(`Holding key ${key} for ${duration}ms with modifiers:`, modifiers);

  try {
    // تحويل المفتاح إلى الصيغة المناسبة
    const mappedKey = mapKeyForHold(key);
    if (!mappedKey) {
      console.error('Failed to map key for holding:', key);
      return false;
    }

    // المفاتيح المعدلة
    const modifierKeys = [];
    if (modifiers && typeof modifiers === 'object') {
      if (modifiers.ctrl) modifierKeys.push('control');
      if (modifiers.alt) modifierKeys.push('alt');
      if (modifiers.shift) modifierKeys.push('shift');
    }

    // ضغط المفاتيح المعدلة أولاً
    if (modifierKeys.length > 0) {
      modifierKeys.forEach(modifier => {
        robot.keyToggle(modifier, 'down');
      });
    }

    // ضغط المفتاح الرئيسي
    robot.keyToggle(mappedKey, 'down');
    console.log(`Key ${mappedKey} pressed down`);

    // تحرير المفتاح بعد المدة المحددة
    setTimeout(() => {
      // تحرير المفتاح الرئيسي
      robot.keyToggle(mappedKey, 'up');
      console.log(`Key ${mappedKey} released after ${duration}ms`);

      // تحرير المفاتيح المعدلة
      if (modifierKeys.length > 0) {
        modifierKeys.forEach(modifier => {
          robot.keyToggle(modifier, 'up');
        });
      }
    }, duration);

    return true;
  } catch (err) {
    console.error('Error in hold key function:', err);
    return false;
  }
}

// دالة مساعدة لتحويل المفتاح للضغط المستمر
function mapKeyForHold(key) {
  // استخدام نفس منطق تحويل المفاتيح الموجود في mapKeyForSim
  const lowerKey = key.toLowerCase().trim();

  // خريطة المفاتيح الخاصة
  const keyMap = {
    'space': 'space',
    'spacebar': 'space',
    ' ': 'space',
    'enter': 'enter',
    'return': 'enter',
    'tab': 'tab',
    'escape': 'escape',
    'esc': 'escape',
    'up': 'up',
    'down': 'down',
    'left': 'left',
    'right': 'right',
    'arrowup': 'up',
    'arrowdown': 'down',
    'arrowleft': 'left',
    'arrowright': 'right',
    'backspace': 'backspace',
    'delete': 'delete',
    'control': 'control',
    'ctrl': 'control',
    'alt': 'alt',
    'shift': 'shift',
    'caps': 'capslock',
    'capslock': 'capslock'
  };

  // إرجاع المفتاح المحول أو المفتاح الأصلي
  return keyMap[lowerKey] || lowerKey;
}

// آخر وقت تمت فيه قراءة تعليق
let lastCommentReadTime = 0;

// قائمة انتظار التعليقات للقراءة
let commentQueue = [];

// معالجة قائمة انتظار التعليقات
async function processCommentQueue() {
  if (commentQueue.length === 0) return;

  const currentComment = commentQueue[0];

  // تحديث وقت آخر قراءة
  lastCommentReadTime = Date.now();

  // تنسيق النص للقراءة
  const textToRead = `${currentComment.author} يقول: ${currentComment.text}`;

  // قراءة النص
  await new Promise((resolve) => {
    speakText(textToRead, resolve);
  });

  // إزالة التعليق من القائمة
  commentQueue.shift();

  // معالجة التعليق التالي في القائمة بعد فترة قصيرة
  if (commentQueue.length > 0) {
    setTimeout(processCommentQueue, 500);
  }
}

// إضافة دالة للتحقق من نماذج TTS المتاحة
async function checkAvailableTTSModels() {
  try {
    // يمكن إضافة منطق هنا للتحقق من النماذج المتاحة على النظام
    // حاليًا، نستخدم قائمة ثابتة من النماذج المدعومة

    console.log('تم تحميل قائمة نماذج TTS المتاحة');
    return ttsSettings.models;
  } catch (error) {
    console.error('خطأ أثناء التحقق من نماذج TTS المتاحة:', error);
    return [];
  }
}

// استدعاء الدالة عند بدء التشغيل
checkAvailableTTSModels().then(models => {
  if (models.length > 0) {
    console.log(`تم العثور على ${models.length} نموذج TTS`);
  } else {
    console.warn('لم يتم العثور على أي نماذج TTS متاحة');
  }
});

// استيراد وظيفة قراءة النص من ملف speakText-edge-tts.js
const { speakText } = require('./speakText-edge-tts.js');

// تصفية التعليقات وإضافتها لقائمة الانتظار
function filterAndQueueComment(comment, author) {
  // تخطي التعليق إذا كانت قراءة التعليقات معطلة
  if (!ttsSettings.enabled) return false;

  // تخطي التعليق إذا لم يمر الوقت المحدد
  const now = Date.now();
  if (now - lastCommentReadTime < ttsSettings.readRate * 1000) return false;

  // تخطي التعليق إذا كان فارغاً
  if (!comment || typeof comment !== 'string' || comment.trim() === '') return false;

  // تصفية التعليق حسب الطول
  if (comment.length < ttsSettings.minLength || comment.length > ttsSettings.maxLength) return false;

  // تصفية التعليق حسب الكلمات المحظورة
  if (ttsSettings.blockedWords.length > 0) {
    const lowerComment = comment.toLowerCase();
    for (const word of ttsSettings.blockedWords) {
      if (word && lowerComment.includes(word.toLowerCase())) return false;
    }
  }

  // إضافة التعليق لقائمة الانتظار
  commentQueue.push({
    text: comment,
    author: author || 'مستخدم'
  });

  // إذا كانت القائمة فارغة، ابدأ معالجة القائمة
  if (commentQueue.length === 1) {
    processCommentQueue();
  }

  return true;
}

/**
 * معالجة أوامر الانضمام للفرق
 */
function handleTeamJoinCommands(data, io) {
  const comment = data.comment.trim().toLowerCase();
  const username = data.nickname || data.uniqueId;

  console.log(`🔍 فحص التعليق: "${comment}" من ${username}`);

  // قائمة الدول بالترتيب (نفس ترتيب الإعدادات)
  const countries = ['EG', 'SA', 'DZ', 'MA', 'IQ', 'AE', 'TN', 'SY', 'LB', 'QA', 'KW', 'BH'];

  // معالجة أمر الانضمام (join 1, join 2, إلخ)
  const joinMatch = comment.match(/^join\s+(\d+)$/);
  console.log(`🔍 نتيجة البحث عن join: ${joinMatch ? 'تم العثور' : 'لم يتم العثور'}`);

  if (joinMatch) {
    const teamNumber = parseInt(joinMatch[1]);
    console.log(`🔢 رقم الفريق: ${teamNumber}`);

    if (teamNumber >= 1 && teamNumber <= countries.length) {
      const countryCode = countries[teamNumber - 1];
      console.log(`🌍 كود الدولة: ${countryCode}`);

      // إرسال أمر الانضمام للعبة
      io.emit('teamJoin', {
        username: username,
        countryCode: countryCode,
        teamNumber: teamNumber
      });

      console.log(`🏆 ${username} انضم للفريق ${teamNumber} (${countryCode})`);
      return;
    } else {
      console.log(`❌ رقم الفريق ${teamNumber} خارج النطاق (1-${countries.length})`);
    }
  }

  // معالجة أمر المغادرة
  if (comment === 'lev' || comment === 'leave') {
    io.emit('teamLeave', {
      username: username
    });

    console.log(`👋 ${username} غادر الفريق`);
    return;
  }

  // معالجة أمر معرفة الفريق الحالي
  if (comment === 'فريقي' || comment === 'myteam') {
    io.emit('teamQuery', {
      username: username
    });
    return;
  }

  // معالجة أمر عرض قائمة الفرق
  if (comment === 'الفرق' || comment === 'teams') {
    io.emit('teamsQuery', {
      username: username
    });
    return;
  }
}

// تصدير إعدادات TTS وserver لاستخدامها في ملفات أخرى
module.exports = {
  ttsSettings,
  server
};

// Start the server
const PORT = process.env.PORT || 3000;
server.listen(PORT, () => {
  console.log(`Server is running on http://localhost:${PORT}`);
  console.log('Use Ctrl+C to stop the server');
  console.log('🛡️ Server-side subscription protection is ACTIVE');
  console.log('🔒 Protected: Gift Mappings, TTS Settings, Profiles, Games');
  console.log('🔐 Authentication required for premium features');
  console.log('🔥 REAL Firebase subscription verification enabled');
  console.log('💰 Only users with active subscriptions can access premium features');
  console.log('🚫 Frontend protection REMOVED - Server-only protection');
  console.log('⚡ Real-time subscription checking on every operation');
  console.log('⏰ Subscription monitoring: Every 1 hour (3600 seconds)');
  console.log('🔐 Firestore Rules: Structured user-specific access control');
  console.log('📋 Protected collections: users/{userId}, settings/{userId}, profiles/{userId}');
  console.log('📖 Subscriptions: Read-only access for verification');
  console.log('🎁 Free tier limits: 5 gift mappings, 1 extra profile, default voice only');

  // بدء مراقبة الاشتراكات المنتهية
  startSubscriptionMonitoring();

  // تصدير إعدادات TTS للاستخدام في ملفات أخرى
  module.exports = {
    ttsSettings
  };
});