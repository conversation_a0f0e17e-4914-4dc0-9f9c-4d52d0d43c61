import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox, simpledialog, filedialog
from TikTokLive import TikTokLiveClient
from TikTokLive.events import CommentEvent, ConnectEvent, GiftEvent, DisconnectEvent, LikeEvent, JoinEvent, FollowEvent
import json
import os
import queue
import keyboard
import time
import threading
from playsound import playsound
from datetime import datetime
import traceback
import re
import pyttsx3
import asyncio
import edge_tts
import tempfile
import requests
import shutil
import subprocess
import functools
from http.server import HTTPServer, SimpleHTTPRequestHandler
import socketserver
import webbrowser
import io


class ModernButton(ttk.Button):
    def __init__(self, master=None, style_name=None, **kwargs):
        super().__init__(master, style=style_name, **kwargs)


class TikTokLiveGUI:
    def __init__(self, root):
        """تهيئة الواجهة الرسومية"""
        self.root = root
        self.root.title("StreamTok")
        self.root.geometry("1000x700")

        # تطبيق النمط الداكن
        self.root.configure(bg="#1e1e1e")
        style = ttk.Style()
        style.theme_use("default")

        # تكوين النمط الداكن
        style.configure("TFrame", background="#1e1e1e")
        style.configure("TLabel", background="#1e1e1e", foreground="#ffffff")
        style.configure("TButton", background="#2d2d2d", foreground="#ffffff")
        style.configure(
            "Treeview",
            background="#2d2d2d",
            foreground="#ffffff",
            fieldbackground="#2d2d2d",
        )
        style.configure("TEntry", background="#2d2d2d", foreground="#ffffff")

        # تهيئة قوائم الانتظار
        self.comments_queue = queue.Queue()
        self.likes_queue = queue.Queue()
        self.gifts_queue = queue.Queue()
        self.tts_queue = queue.Queue()

        # تهيئة المتغيرات
        self.client = None
        self.is_connected = False
        self.key_bindings = {}
        self.profiles = {}
        self.current_profile = None
        self.gifts_data = {}

        # تهيئة متغيرات الاتصال
        self.reconnect_attempts = 0
        self.max_reconnect_attempts = 5
        self.connection_error_sound = os.path.join("sounds", "connection_error.mp3")

        # تهيئة متغيرات TTS
        self.tts_enabled = False
        self.tts_language = "ar"
        self.is_speaking = False
        self.voices = {
            "ar": {
                "ar-EG-ShakirNeural": "شاكر (مصري)",
                "ar-EG-SalmaNeural": "سلمى (مصرية)",
                "ar-SA-ZariyahNeural": "زارية (سعودية)",
                "ar-SA-HamedNeural": "حامد (سعودي)",
                "ar-AE-FatimaNeural": "فاطمة (إماراتية)",
                "ar-AE-HamdanNeural": "حمدان (إماراتي)",
                "ar-KW-NouraNeural": "نورة (كويتية)",
                "ar-KW-FahedNeural": "فهد (كويتي)",
                "ar-QA-AmalNeural": "أمل (قطرية)",
                "ar-QA-MoazNeural": "معاذ (قطري)",
            },
            "en": {
                "en-US-ChristopherNeural": "Christopher (American)",
                "en-US-JennyNeural": "Jenny (American)",
                "en-US-GuyNeural": "Guy (American)",
                "en-GB-SoniaNeural": "Sonia (British)",
                "en-GB-RyanNeural": "Ryan (British)",
                "en-AU-NatashaNeural": "Natasha (Australian)",
                "en-AU-WilliamNeural": "William (Australian)",
                "en-CA-ClaraNeural": "Clara (Canadian)",
                "en-CA-LiamNeural": "Liam (Canadian)",
                "en-IE-ConnorNeural": "Connor (Irish)",
            },
        }

        # تخزين القيم المعروضة ومعرفاتها
        self.voice_name_to_id = {
            name: id
            for lang_voices in self.voices.values()
            for id, name in lang_voices.items()
        }

        # تهيئة الصوت الافتراضي
        self.selected_voice = tk.StringVar()
        default_voice = (
            "ar-EG-ShakirNeural"
            if self.tts_language == "ar"
            else "en-US-ChristopherNeural"
        )
        self.selected_voice.set(default_voice)

        # إنشاء المجلدات المطلوبة
        self.sounds_dir = os.path.join(
            os.path.dirname(os.path.abspath(__file__)), "sounds"
        )
        if not os.path.exists(self.sounds_dir):
            os.makedirs(self.sounds_dir)

        self.temp_dir = tempfile.mkdtemp()

        # تهيئة مسارات الأصوات
        self.connect_sound = os.path.join(self.sounds_dir, "connect.wav")
        self.disconnect_sound = os.path.join(self.sounds_dir, "disconnect.wav")

        # تحميل البيانات
        self.load_gifts()
        self.load_profiles()

        # إنشاء الواجهة
        self.setup_styles()
        self.create_gui()

        # تحديث العرض
        self.update_keys_display()
        self.update_profile_name()

        # بدء معالجة الرسائل
        self.root.after(100, self.process_messages)

        self.video_server = None
        self.video_port = 8000
        self.video_url = None
        self.media_server = None
        self.media_port = 8000
        self.media_url = None

    def setup_styles(self):
        """تهيئة الأنماط"""
        style = ttk.Style()

        # الألوان الأساسية
        bg_color = "#1e1e1e"  # خلفية داكنة
        fg_color = "#ffffff"  # نص أبيض
        accent_color = "#007acc"  # لون مميز أزرق
        green_color = "#28a745"  # لون أخضر للاتصال
        blue_color = "#007bff"  # لون أزرق لمحاكاة المفاتيح
        red_color = "#dc3545"  # لون أحمر للحذف

        # نمط الإطارات
        style.configure("Main.TFrame", background=bg_color)
        style.configure("Header.TFrame", background=bg_color)

        # نمط التسميات
        style.configure(
            "Modern.TLabel",
            background=bg_color,
            foreground=fg_color,
            font=("Arial", 11),
        )

        # نمط حقل اسم المستخدم
        style.configure(
            "Username.TEntry",
            fieldbackground="#2d2d2d",
            foreground=fg_color,
            font=("Arial", 11, "bold"),
            padding=10,
        )

        # نمط الأزرار الأساسي
        style.configure(
            "Modern.TButton",
            background=accent_color,
            foreground=fg_color,
            padding=(15, 8),
            font=("Arial", 10, "bold"),
            borderwidth=0,
            relief="flat",
        )

        # نمط زر الاتصال
        style.configure(
            "Connect.TButton",
            background=green_color,
            foreground=fg_color,
            padding=(15, 8),
            font=("Arial", 11, "bold"),
            borderwidth=0,
            relief="flat",
        )
        style.map(
            "Connect.TButton",
            background=[("active", "#218838")],
            foreground=[("active", fg_color)],
        )

        # نمط زر المفاتيح
        style.configure(
            "Keys.TButton",
            background=blue_color,
            foreground=fg_color,
            padding=(15, 8),
            font=("Arial", 11, "bold"),
            borderwidth=0,
            relief="flat",
        )
        style.map(
            "Keys.TButton",
            background=[("active", "#0056b3")],
            foreground=[("active", fg_color)],
        )

        # نمط زر الحذف
        style.configure(
            "Delete.TButton",
            background=red_color,
            foreground=fg_color,
            padding=(15, 8),
            font=("Arial", 11, "bold"),
            borderwidth=0,
            relief="flat",
        )
        style.map(
            "Delete.TButton",
            background=[("active", "#c82333")],
            foreground=[("active", fg_color)],
        )

        # نمط الإطارات المحيطة
        style.configure(
            "Modern.TLabelframe",
            background=bg_color,
            foreground=fg_color,
            font=("Arial", 11, "bold"),
            borderwidth=1,
            relief="solid",
        )

        style.configure(
            "Modern.TLabelframe.Label",
            background=bg_color,
            foreground=fg_color,
            font=("Arial", 11, "bold"),
        )

        # نمط القوائم المنسدلة
        style.configure(
            "TCombobox",
            background=bg_color,
            foreground=fg_color,
            fieldbackground="#2d2d2d",
            arrowcolor=fg_color,
            font=("Arial", 10),
        )

        # نمط أزرار الراديو
        style.configure(
            "TRadiobutton", background=bg_color, foreground=fg_color, font=("Arial", 10)
        )

        # تعيين خلفية النافذة الرئيسية
        self.root.configure(bg=bg_color)

    def load_gifts(self):
        """تحميل قائمة الهدايا من الرابط"""
        try:
            gifts_url = "https://webcast.tiktok.com/webcast/gift/list/?aid=1988"
            response = requests.get(gifts_url)
            if response.status_code == 200:
                data = response.json()
                # استخراج الهدايا من الاستجابة
                if "data" in data and isinstance(data["data"], dict):
                    gift_list = data["data"].get("gifts", [])
                    self.gifts_data = {
                        str(gift["id"]): gift for gift in gift_list if "id" in gift
                    }
                else:
                    self.log_comment("❌ تنسيق البيانات غير صالح")
            else:
                self.log_comment(
                    f"❌ خطأ في تحميل قائمة الهدايا: {response.status_code}"
                )
        except Exception as e:
            self.log_comment(f"❌ خطأ في تحميل قائمة الهدايا: {str(e)}")
            self.gifts_data = {}  # تهيئة قاموس فارغ في حالة الفشل

    def load_profiles(self):
        """تحميل البروفايلات من الملف"""
        try:
            if os.path.exists("profiles.json"):
                with open("profiles.json", "r", encoding="utf-8") as f:
                    data = json.load(f)

                # تحميل البروفايلات
                if isinstance(data, dict):
                    if "profiles" in data:
                        self.profiles = data["profiles"]
                        if (
                            "current_profile" in data
                            and data["current_profile"] in self.profiles
                        ):
                            self.current_profile = data["current_profile"]
                            self.key_bindings = (
                                self.profiles[self.current_profile]
                                .get("bindings", {})
                                .copy()
                            )
                    else:
                        # التوافق مع النسخ القديمة
                        self.profiles = data

                self.log_comment("✅ تم تحميل البروفايلات بنجاح")
            else:
                self.profiles = {}
                self.current_profile = None
                self.key_bindings = {}
                self.log_comment("ℹ️ لم يتم العثور على ملف البروفايلات")
        except Exception as e:
            self.log_comment(f"❌ خطأ في تحميل البروفايلات: {str(e)}")
            self.profiles = {}
            self.current_profile = None
            self.key_bindings = {}

    def save_profiles(self):
        """حفظ البروفايلات في الملف"""
        try:
            # تحديث البروفايل الحالي قبل الحفظ
            if self.current_profile:
                self.profiles[self.current_profile] = {
                    "bindings": self.key_bindings.copy()
                }

            # حفظ جميع البروفايلات
            data = {"profiles": self.profiles, "current_profile": self.current_profile}

            with open("profiles.json", "w", encoding="utf-8") as f:
                json.dump(data, f, ensure_ascii=False, indent=4)
            self.log_comment("✅ تم حفظ البروفايلات بنجاح")
        except Exception as e:
            self.log_comment(f"❌ خطأ في حفظ البروفايلات: {str(e)}")

    def update_profiles_list(self):
        """تحديث قائمة البروفايلات"""
        try:
            # مسح القائمة
            self.profiles_list.delete(0, tk.END)

            # إضافة البروفايلات
            for name in sorted(self.profiles.keys()):
                self.profiles_list.insert(tk.END, name)

            # تحديد البروفايل الحالي إذا وجد
            if self.current_profile:
                try:
                    index = sorted(self.profiles.keys()).index(self.current_profile)
                    self.profiles_list.selection_set(index)
                except ValueError:
                    pass

        except Exception as e:
            self.log_comment(f"❌ خطأ في تحديث قائمة البروفايلات: {str(e)}")

    def on_profile_selected(self, event=None):
        """معالجة حدث اختيار بروفايل"""
        try:
            selected = self.profiles_list.get()
            if not selected:
                return

            # تحميل البروفايل المحدد
            profile_data = self.profiles[selected]
            self.key_bindings = profile_data.get("bindings", {}).copy()
            self.current_profile = selected

            # تحديث العرض
            self.update_keys_display()
            self.log_comment(f"✅ تم تحميل البروفايل: {selected}")

        except Exception as e:
            self.log_comment(f"❌ خطأ في تحميل البروفايل: {str(e)}")

    def delete_profile(self):
        """حذف البروفايل المحدد"""
        try:
            selected = self.profiles_list.get()
            if not selected:
                self.log_comment("⚠️ الرجاء اختيار بروفايل للحذف")
                return

            if selected == self.current_profile:
                self.log_comment("❌ لا يمكن حذف البروفايل النشط!")
                return

            if messagebox.askyesno(
                "تأكيد الحذف", f"هل أنت متأكد من حذف البروفايل '{selected}'؟"
            ):
                del self.profiles[selected]
                self.save_profiles()
                self.update_profiles_list()
                self.log_comment(f"✅ تم حذف البروفايل: {selected}")

        except Exception as e:
            self.log_comment(f"❌ خطأ في حذف البروفايل: {str(e)}")

    def manage_profiles(self):
        """إدارة البروفايلات"""
        dialog = tk.Toplevel(self.root)
        dialog.title("إدارة البروفايلات")
        dialog.geometry("600x500")
        dialog.configure(bg="#1e1e1e")

        # الإطار الرئيسي
        main_frame = ttk.Frame(dialog, style="Modern.TFrame", padding="20")
        main_frame.pack(fill="both", expand=True)

        # إطار معلومات البروفايل الحالي
        current_profile_frame = ttk.LabelFrame(
            main_frame,
            text="البروفايل الحالي",
            style="Modern.TLabelframe",
            padding="10",
        )
        current_profile_frame.pack(fill="x", pady=(0, 10))

        # عرض اسم البروفايل الحالي
        current_profile_label = ttk.Label(
            current_profile_frame,
            text=f"البروفايل النشط: {self.current_profile if self.current_profile else 'لا يوجد'}",
            style="Modern.TLabel",
        )
        current_profile_label.pack(fill="x", padx=5, pady=5)

        # عدد المفاتيح في البروفايل الحالي
        keys_count = 0
        if self.current_profile and self.current_profile in self.profiles:
            keys_count = len(self.profiles[self.current_profile].get("bindings", {}))

        self.keys_label = ttk.Label(
            current_profile_frame,
            text=f"عدد المفاتيح المحفوظة: {keys_count}",
            style="Modern.TLabel",
        )
        self.keys_label.pack(fill="x", padx=5)

        # إطار قائمة البروفايلات
        profiles_frame = ttk.LabelFrame(
            main_frame,
            text="البروفايلات المتوفرة",
            style="Modern.TLabelframe",
            padding="10",
        )
        profiles_frame.pack(fill="both", expand=True, pady=(0, 10))

        # إنشاء قائمة البروفايلات مع شريط التمرير
        profiles_list_frame = ttk.Frame(profiles_frame)
        profiles_list_frame.pack(fill="both", expand=True)

        # شريط التمرير
        scrollbar = ttk.Scrollbar(profiles_list_frame)
        scrollbar.pack(side="right", fill="y")

        # قائمة البروفايلات
        profiles_listbox = tk.Listbox(
            profiles_list_frame,
            bg="#2d2d2d",
            fg="white",
            selectmode="single",
            yscrollcommand=scrollbar.set,
        )
        profiles_listbox.pack(side="left", fill="both", expand=True)
        scrollbar.config(command=profiles_listbox.yview)

        # تحديث قائمة البروفايلات
        for profile in sorted(self.profiles.keys()):
            profiles_listbox.insert("end", profile)
            if profile == self.current_profile:
                profiles_listbox.selection_set("end")

        # إطار الأزرار
        buttons_frame = ttk.Frame(main_frame, style="Modern.TFrame")
        buttons_frame.pack(fill="x", pady=10)

        def create_new_profile():
            """إنشاء بروفايل جديد"""
            name = simpledialog.askstring("إنشاء بروفايل", "أدخل اسم البروفايل الجديد:")
            if name:
                name = name.strip()
                if not name:
                    self.log_comment("❌ الرجاء إدخال اسم صحيح للبروفايل")
                    return
                if name in self.profiles:
                    self.log_comment("❌ هذا البروفايل موجود مسبقاً")
                    return

                # إنشاء بروفايل جديد
                self.profiles[name] = {"bindings": {}}
                self.current_profile = name
                self.key_bindings = {}
                self.save_profiles()

                # تحديث القائمة
                profiles_listbox.insert("end", name)
                profiles_listbox.selection_clear(0, "end")
                profiles_listbox.selection_set("end")

                # تحديث العرض
                current_profile_label.config(text=f"البروفايل النشط: {name}")
                self.keys_label.config(text="عدد المفاتيح المحفوظة: 0")

                self.log_comment(f"✅ تم إنشاء البروفايل: {name}")

        def load_selected_profile():
            """تحميل البروفايل المحدد"""
            selection = profiles_listbox.curselection()
            if not selection:
                self.log_comment("⚠️ الرجاء تحديد بروفايل")
                return

            profile_name = profiles_listbox.get(selection[0])
            if profile_name in self.profiles:
                # حفظ البروفايل الحالي قبل التغيير
                if self.current_profile:
                    self.profiles[self.current_profile] = {
                        "bindings": self.key_bindings.copy()
                    }

                # تحميل البروفايل الجديد
                self.key_bindings = (
                    self.profiles[profile_name].get("bindings", {}).copy()
                )
                self.current_profile = profile_name
                self.save_profiles()
                self.update_keys_display()

                # تحديث العرض
                current_profile_label.config(text=f"البروفايل النشط: {profile_name}")
                keys_count = len(self.key_bindings)
                self.keys_label.config(text=f"عدد المفاتيح المحفوظة: {keys_count}")

                self.log_comment(f"✅ تم تحميل البروفايل: {profile_name}")

        def delete_selected_profile():
            """حذف البروفايل المحدد"""
            selection = profiles_listbox.curselection()
            if not selection:
                self.log_comment("⚠️ الرجاء تحديد بروفايل للحذف")
                return

            profile_name = profiles_listbox.get(selection[0])
            if messagebox.askyesno(
                "تأكيد الحذف", f"هل أنت متأكد من حذف البروفايل '{profile_name}'؟"
            ):
                if profile_name == self.current_profile:
                    self.current_profile = None
                    self.key_bindings = {}
                    self.update_keys_display()
                    current_profile_label.config(text="البروفايل النشط: لا يوجد")
                    self.keys_label.config(text="عدد المفاتيح المحفوظة: 0")

                del self.profiles[profile_name]
                self.save_profiles()
                profiles_listbox.delete(selection[0])
                self.log_comment(f"✅ تم حذف البروفايل: {profile_name}")

        def import_profiles():
            """استيراد البروفايلات"""
            file_path = filedialog.askopenfilename(
                filetypes=[("ملفات البروفايلات", "*.json"), ("جميع الملفات", "*.*")],
                title="استيراد البروفايلات",
            )
            if file_path:
                try:
                    with open(file_path, "r", encoding="utf-8") as f:
                        imported_profiles = json.load(f)

                    # التحقق من صحة البيانات المستوردة
                    if not isinstance(imported_profiles, dict):
                        raise ValueError("تنسيق الملف غير صحيح")

                    # دمج البروفايلات المستوردة مع الحالية
                    self.profiles.update(imported_profiles)
                    self.save_profiles()

                    # تحديث القائمة
                    profiles_listbox.delete(0, "end")
                    for profile in sorted(self.profiles.keys()):
                        profiles_listbox.insert("end", profile)

                    self.log_comment("✅ تم استيراد البروفايلات بنجاح")
                except Exception as e:
                    self.log_comment(f"❌ خطأ في استيراد البروفايلات: {str(e)}")

        def export_profiles():
            """تصدير البروفايلات"""
            if not self.profiles:
                self.log_comment("⚠️ لا توجد بروفايلات للتصدير")
                return

            file_path = filedialog.asksaveasfilename(
                defaultextension=".json",
                filetypes=[("ملفات البروفايلات", "*.json"), ("جميع الملفات", "*.*")],
                title="تصدير البروفايلات",
            )
            if file_path:
                try:
                    with open(file_path, "w", encoding="utf-8") as f:
                        json.dump(self.profiles, f, ensure_ascii=False, indent=4)
                    self.log_comment("✅ تم تصدير البروفايلات بنجاح")
                except Exception as e:
                    self.log_comment(f"❌ خطأ في تصدير البروفايلات: {str(e)}")

        # أزرار التحكم
        ModernButton(
            buttons_frame,
            text="➕ إنشاء",
            command=create_new_profile,
            style_name="Modern.TButton",
        ).pack(side="left", padx=5)

        ModernButton(
            buttons_frame,
            text="📂 تحميل",
            command=load_selected_profile,
            style_name="Modern.TButton",
        ).pack(side="left", padx=5)

        ModernButton(
            buttons_frame,
            text="❌ حذف",
            command=delete_selected_profile,
            style_name="Modern.TButton",
        ).pack(side="left", padx=5)

        ModernButton(
            buttons_frame,
            text="📥 استيراد",
            command=import_profiles,
            style_name="Modern.TButton",
        ).pack(side="left", padx=5)

        ModernButton(
            buttons_frame,
            text="📤 تصدير",
            command=export_profiles,
            style_name="Modern.TButton",
        ).pack(side="left", padx=5)

        ModernButton(
            buttons_frame,
            text="✖️ إغلاق",
            command=dialog.destroy,
            style_name="Modern.TButton",
        ).pack(side="right", padx=5)

        # جعل النافذة مركزية
        dialog.transient(self.root)
        dialog.grab_set()
        dialog.focus()

    def save_profiles(self):
        """حفظ البروفايلات في الملف"""
        try:
            # تحديث البروفايل الحالي قبل الحفظ
            if self.current_profile:
                self.profiles[self.current_profile] = {
                    "bindings": self.key_bindings.copy()
                }

            # حفظ جميع البروفايلات
            data = {"profiles": self.profiles, "current_profile": self.current_profile}

            with open("profiles.json", "w", encoding="utf-8") as f:
                json.dump(data, f, ensure_ascii=False, indent=4)
            self.log_comment("✅ تم حفظ البروفايلات بنجاح")
        except Exception as e:
            self.log_comment(f"❌ خطأ في حفظ البروفايلات: {str(e)}")

    def load_profiles(self):
        """تحميل البروفايلات من الملف"""
        try:
            if os.path.exists("profiles.json"):
                with open("profiles.json", "r", encoding="utf-8") as f:
                    data = json.load(f)

                # تحميل البروفايلات
                if isinstance(data, dict):
                    if "profiles" in data:
                        self.profiles = data["profiles"]
                        if (
                            "current_profile" in data
                            and data["current_profile"] in self.profiles
                        ):
                            self.current_profile = data["current_profile"]
                            self.key_bindings = (
                                self.profiles[self.current_profile]
                                .get("bindings", {})
                                .copy()
                            )
                    else:
                        # التوافق مع النسخ القديمة
                        self.profiles = data

                self.log_comment("✅ تم تحميل البروفايلات بنجاح")
            else:
                self.profiles = {}
                self.current_profile = None
                self.key_bindings = {}
                self.log_comment("ℹ️ لم يتم العثور على ملف البروفايلات")
        except Exception as e:
            self.log_comment(f"❌ خطأ في تحميل البروفايلات: {str(e)}")
            self.profiles = {}
            self.current_profile = None
            self.key_bindings = {}

    def create_gui(self):
        """إنشاء واجهة المستخدم"""
        # تهيئة النافذة الرئيسية
        main_frame = ttk.Frame(self.root, style="Main.TFrame")
        main_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # إطار التحكم العلوي
        control_frame = ttk.Frame(main_frame, style="Header.TFrame")
        control_frame.pack(fill="x", pady=(0, 10))

        # إطار اسم المستخدم المحسن
        username_container = ttk.Frame(control_frame, style="UsernameFrame.TFrame")
        username_container.pack(side="left", padx=10)

        # أيقونة المستخدم
        ttk.Label(username_container, text="👤", style="Username.TLabel").pack(
            side="left", padx=(5, 0)
        )

        # حقل اسم المستخدم
        self.username_var = tk.StringVar()
        if hasattr(self.root, "username"):
            self.username_var.set(self.root.username)  # استعادة الاسم السابق

        username_entry = ttk.Entry(
            username_container,
            textvariable=self.username_var,
            style="Username.TEntry",
            width=25,
        )
        username_entry.pack(side="left", padx=5, pady=5)

        # زر الاتصال
        self.connect_btn = ModernButton(
            control_frame,
            text="اتصال",
            command=self.toggle_connection,
            style_name="Connect.TButton",
        )
        self.connect_btn.pack(side="left", padx=5)

        # زر إضافة محاكاة مفتاح
        self.add_key_btn = ModernButton(
            control_frame,
            text="✨ إضافة محاكاة مفتاح",
            command=self.add_key_binding,
            style_name="Keys.TButton",
        )
        self.add_key_btn.pack(side="left", padx=5)

        # زر إدارة البروفايلات
        manage_profiles_button = ModernButton(
            control_frame,
            text="👥 إدارة البروفايلات",
            command=self.manage_profiles,
            style_name="Modern.TButton",
        )
        manage_profiles_button.pack(side="left", padx=5)

        # حالة الاتصال
        self.status_var = tk.StringVar(value="غير متصل")
        status_label = ttk.Label(
            main_frame, textvariable=self.status_var, style="Status.TLabel"
        )
        status_label.pack(pady=5)

        # منطقة محاكاة المفاتيح
        keys_frame = ttk.LabelFrame(
            main_frame, text="محاكاة المفاتيح", style="Modern.TLabelframe"
        )
        keys_frame.pack(fill="both", expand=True, pady=(0, 15))

        # إطار الأزرار لمحاكاة المفاتيح
        keys_buttons_frame = ttk.Frame(keys_frame)
        keys_buttons_frame.pack(fill="x", pady=5, padx=5)

        # زر تعديل الإعدادات
        edit_btn = ModernButton(
            keys_buttons_frame,
            text="✏️ تعديل",
            command=self.edit_selected_key,
            style_name="Keys.TButton",
        )
        edit_btn.pack(side="left", padx=5)

        # زر حذف الإعدادات
        delete_btn = ModernButton(
            keys_buttons_frame,
            text="🗑️ حذف",
            command=self.delete_selected_key,
            style_name="Delete.TButton",
        )
        delete_btn.pack(side="left", padx=5)

        # زر حفظ الإعدادات
        save_btn = ModernButton(
            keys_buttons_frame,
            text="💾 حفظ الإعدادات",
            command=self.save_key_bindings,
            style_name="Keys.TButton",
        )
        save_btn.pack(side="right", padx=5)

        # منطقة محاكاة المفاتيح
        self.simulation_text = scrolledtext.ScrolledText(
            keys_frame,
            height=15,
            bg="#2d2d2d",
            fg="#ffffff",
            font=("Arial", 12),
            insertbackground="white",
        )
        self.simulation_text.pack(pady=5, padx=5, fill="both", expand=True)

        # إطار المحتوى
        content_frame = ttk.Frame(main_frame, style="Modern.TFrame")
        content_frame.pack(fill="both", expand=True)

        # تقسيم الشاشة أفقياً للتعليقات والهدايا
        content_frame.columnconfigure(0, weight=1)
        content_frame.columnconfigure(1, weight=1)

        # منطقة التعليقات
        comments_frame = ttk.LabelFrame(
            content_frame, text="التعليقات", style="Modern.TLabelframe"
        )
        comments_frame.grid(row=0, column=0, padx=5, sticky="nsew")

        self.comments_area = scrolledtext.ScrolledText(
            comments_frame,
            height=12,
            bg="#2d2d2d",
            fg="#ffffff",
            font=("Arial", 11),
            insertbackground="white",
        )
        self.comments_area.pack(pady=5, padx=5, fill="both", expand=True)

        # منطقة الهدايا
        gifts_frame = ttk.LabelFrame(
            content_frame, text="الهدايا", style="Modern.TLabelframe"
        )
        gifts_frame.grid(row=0, column=1, padx=5, sticky="nsew")

        self.gifts_area = scrolledtext.ScrolledText(
            gifts_frame,
            height=12,
            bg="#2d2d2d",
            fg="#ffffff",
            font=("Arial", 11),
            insertbackground="white",
        )
        self.gifts_area.pack(pady=5, padx=5, fill="both", expand=True)

        # إطار إعدادات قراءة التعليقات
        tts_frame = ttk.LabelFrame(
            main_frame, text="قراءة التعليقات", style="Modern.TLabelframe"
        )
        tts_frame.pack(fill="x", padx=5, pady=5)

        # زر تفعيل/تعطيل قراءة التعليقات
        self.tts_button = ModernButton(
            tts_frame,
            text="تفعيل القراءة",
            style_name="Success.TButton",
            command=self.toggle_tts,
        )
        self.tts_button.pack(side="left", padx=5)

        # قائمة اختيار اللغة
        self.language_var = tk.StringVar(value="ar")
        language_frame = ttk.Frame(tts_frame)
        language_frame.pack(side="left", padx=5)

        ttk.Radiobutton(
            language_frame,
            text="العربية",
            variable=self.language_var,
            value="ar",
            command=self.update_tts_language,
        ).pack(side="left")

        ttk.Radiobutton(
            language_frame,
            text="الإنجليزية",
            variable=self.language_var,
            value="en",
            command=self.update_tts_language,
        ).pack(side="left")

        # إطار اختيار الصوت
        voice_frame = ttk.Frame(tts_frame)
        voice_frame.pack(side="left", padx=5)

        ttk.Label(voice_frame, text="اختر الصوت:").pack(side="left", padx=2)

        # قائمة منسدلة لاختيار الصوت
        self.voice_menu = ttk.Combobox(
            voice_frame, textvariable=self.selected_voice, state="readonly", width=30
        )
        self.update_voice_list()  # تحديث قائمة الأصوات
        self.voice_menu.pack(side="left", padx=2)

        # إطار السجل
        log_frame = ttk.LabelFrame(main_frame, text="السجل", style="Custom.TLabelframe")
        log_frame.pack(fill="x", padx=5, pady=5)

        # عرض السجل
        self.log_text = tk.Text(log_frame, height=5, bg="#2d2d2d", fg="white")
        self.log_text.pack(fill="x", padx=5, pady=5)

        # إضافة شريط التمرير للسجل
        log_scrollbar = ttk.Scrollbar(log_frame, command=self.log_text.yview)
        log_scrollbar.pack(side="right", fill="y")
        self.log_text.configure(yscrollcommand=log_scrollbar.set)

        # تطبيق النمط على السجل
        self.log_text.tag_configure("success", foreground="#4CAF50")
        self.log_text.tag_configure("error", foreground="#F44336")
        self.log_text.tag_configure("warning", foreground="#FFC107")
        self.log_text.tag_configure("info", foreground="#2196F3")

        self.update_keys_display()

    def add_key_binding(self):
        """إضافة محاكاة مفتاح جديدة"""
        dialog = tk.Toplevel(self.root)
        dialog.title("إضافة محاكاة مفتاح")
        dialog.geometry("500x750")  # زيادة ارتفاع النافذة
        dialog.resizable(False, False)
        dialog.configure(bg="#1e1e1e")  # إضافة خلفية داكنة

        # تطبيق النمط
        style = ttk.Style()
        style.configure("Dialog.TFrame", background="#1e1e1e")
        main_frame = ttk.Frame(dialog, style="Dialog.TFrame", padding="10")
        main_frame.pack(fill="both", expand=True)

        # إطار اختيار الهدية
        gift_frame = ttk.Frame(main_frame, style="Dialog.TFrame")
        gift_frame.pack(fill="x", pady=(0, 10))

        # إعداد قائمة الأسماء والأيدي
        gift_names = [gift["name"] for gift in self.gifts_data.values()]
        gift_ids = list(self.gifts_data.keys())

        # القائمة المنسدلة للهدايا
        ttk.Label(gift_frame, text="الهدية:", style="Dialog.TLabel").pack(
            side="left", padx=5
        )
        self.gift_combobox = ttk.Combobox(
            gift_frame,
            values=gift_names,
            state="readonly",
            width=25,
            style="Modern.TCombobox",
        )
        self.gift_combobox.pack(side="left", padx=5)

        if gift_names:
            # اختيار أول هدية بشكل افتراضي
            self.gift_combobox.set(gift_names[0])

        # حقل الاسم
        ttk.Label(main_frame, text="الاسم:", style="Dialog.TLabel").pack(
            anchor="w", pady=5
        )
        name_entry = ttk.Entry(main_frame, style="Username.TEntry")
        name_entry.pack(fill="x", padx=20)

        # إطار تعليمات تنسيق المفاتيح
        help_frame = ttk.LabelFrame(
            main_frame, text="تنسيق المفاتيح:", style="Dialog.TLabelframe", padding=10
        )
        help_frame.pack(fill="x", pady=10)

        help_text = """• استخدم + للجمع بين المفاتيح: ctrl+shift+a
• استخدم @ للتأخير: a@200 (200 مللي ثانية)
• استخدم ; للفصل بين التسلسلات: a@100;b@200
• المفاتيح الخاصة: space, backspace, enter, tab, esc
  up, down, left, right
• المعدلات: ctrl, alt, shift"""

        help_label = ttk.Label(
            help_frame, text=help_text, style="Dialog.TLabel", justify="right"
        )
        help_label.pack(anchor="e")  # تغيير المحاذاة إلى اليمين

        # تطبيق النمط الداكن على إطار التنسيق فقط
        style.configure("Dialog.TLabelframe", background="#2d2d2d", foreground="white")
        style.configure(
            "Dialog.TLabelframe.Label", background="#2d2d2d", foreground="white"
        )

        # المفاتيح
        ttk.Label(main_frame, text="المفاتيح:", style="Dialog.TLabel").pack(
            anchor="w", pady=5
        )
        keys_entry = ttk.Entry(main_frame, style="Username.TEntry")
        keys_entry.pack(fill="x", padx=20)

        # الصوت
        sound_frame = ttk.Frame(main_frame, style="Dialog.TFrame")
        sound_frame.pack(fill="x", pady=(10, 5))
        ttk.Label(sound_frame, text="الصوت:", style="Dialog.TLabel").pack(
            side="left", padx=5
        )
        sound_var = tk.StringVar()
        sound_entry = ttk.Entry(
            sound_frame, textvariable=sound_var, style="Username.TEntry", width=30
        )
        sound_entry.pack(side="left", fill="x", expand=True)
        browse_button = ModernButton(
            sound_frame,
            text="📂",
            width=3,
            command=lambda: self.browse_sound_file(sound_var),
        )
        browse_button.pack(side="right", padx=5)

        # إضافة حقل لاختيار الوسائط (فيديو أو صورة)
        media_frame = ttk.Frame(main_frame, style="Dialog.TFrame")
        media_frame.pack(fill="x", pady=(10, 5))
        ttk.Label(media_frame, text="الوسائط:", style="Dialog.TLabel").pack(
            side="left", padx=5
        )
        media_var = tk.StringVar()
        media_entry = ttk.Entry(
            media_frame, textvariable=media_var, style="Username.TEntry", width=30
        )
        media_entry.pack(side="left", fill="x", expand=True)
        browse_media_button = ModernButton(
            media_frame,
            text="📂",
            width=3,
            command=lambda: self.browse_media_file(media_var),
        )
        browse_media_button.pack(side="right", padx=5)

        # إضافة خيارات التشغيل
        trigger_frame = ttk.Frame(main_frame, style="Dialog.TFrame")
        trigger_frame.pack(fill="x", pady=(10, 5))
        ttk.Label(trigger_frame, text="تشغيل عند:", style="Dialog.TLabel").pack(
            side="left", padx=5
        )
        trigger_var = tk.StringVar(value="gift")
        ttk.Radiobutton(trigger_frame, text="هدية", variable=trigger_var, value="gift").pack(side="left")
        ttk.Radiobutton(trigger_frame, text="انضمام", variable=trigger_var, value="join").pack(side="left")
        ttk.Radiobutton(trigger_frame, text="لايك", variable=trigger_var, value="like").pack(side="left")
        ttk.Radiobutton(trigger_frame, text="متابعة", variable=trigger_var, value="follow").pack(side="left")

        # حقل لإدخال اسم المستخدم أو عدد اللايكات أو معرف الهدية
        trigger_value_frame = ttk.Frame(main_frame, style="Dialog.TFrame")
        trigger_value_frame.pack(fill="x", pady=(5, 10))
        self.trigger_value_label = ttk.Label(trigger_value_frame, text="القيمة:", style="Dialog.TLabel")
        self.trigger_value_label.pack(side="left", padx=5)
        self.trigger_value_entry = ttk.Entry(trigger_value_frame, style="Username.TEntry")
        self.trigger_value_entry.pack(side="left", fill="x", expand=True)

        def update_trigger_value_label(*args):
            if trigger_var.get() == "gift":
                self.trigger_value_label.config(text="معرف الهدية:")
            elif trigger_var.get() == "join":
                self.trigger_value_label.config(text="اسم المستخدم:")
            elif trigger_var.get() == "like":
                self.trigger_value_label.config(text="عدد اللايكات:")
            else:
                self.trigger_value_label.config(text="اسم المستخدم:")

        trigger_var.trace("w", update_trigger_value_label)

        # إضافة خيار لإخفاء الوسائط
        hide_media_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(
            main_frame, 
            text="إخفاء الوسائط عند عدم التشغيل", 
            variable=hide_media_var,
            style="TCheckbutton"
        ).pack(fill="x", pady=5)

        # إضافة حقل لتحديد مدة العرض
        duration_frame = ttk.Frame(main_frame, style="Dialog.TFrame")
        duration_frame.pack(fill="x", pady=5)
        ttk.Label(duration_frame, text="مدة العرض (مللي ثانية):", style="Dialog.TLabel").pack(
            side="left", padx=5
        )
        duration_var = tk.StringVar(value="5000")
        ttk.Entry(
            duration_frame, textvariable=duration_var, style="Username.TEntry", width=10
        ).pack(side="left", padx=5)

        def test_keys():
            keys = keys_entry.get().strip()
            if not keys:
                self.log_comment("❌ الرجاء إدخال المفاتيح!")
                return

            success, message = self.test_keys(keys)
            self.log_comment(message)

        def test_sound():
            sound = sound_var.get().strip()
            if not sound:
                self.log_comment("❌ الرجاء اختيار ملف صوتي!")
                return

            success, message = self.test_sound(sound)
            self.log_comment(message)

        def test_media():
            media_path = media_var.get().strip()
            if not media_path:
                self.log_comment("❌ الرجاء اختيار ملف وسائط!")
                return

            duration = int(duration_var.get())
            self.show_media(media_path, duration)

        def save():
            selected_gift_name = self.gift_combobox.get().strip()
            if not selected_gift_name:
                self.log_comment("❌ الرجاء اختيار هدية!")
                return

            name = name_entry.get().strip()
            if not name:
                self.log_comment("❌ الرجاء إدخال الاسم!")
                return

            keys = keys_entry.get().strip()
            if not keys:
                self.log_comment("❌ الرجاء إدخال المفاتيح!")
                return

            # الحصول على معرف الهدية المحددة
            selected_index = gift_names.index(selected_gift_name)
            selected_gift_id = gift_ids[selected_index]

            media_path = media_var.get().strip()
            trigger_type = trigger_var.get()
            trigger_value = self.trigger_value_entry.get().strip()
            hide_media = hide_media_var.get()
            duration = int(duration_var.get())

            if media_path:
                # نسخ الوسائط إلى المجلد الحالي
                media_filename = os.path.basename(media_path)
                shutil.copy2(media_path, media_filename)
                self.create_media_html(media_filename, hide_media)
                self.start_media_server()

            # تحديث القاموس
            self.key_bindings[selected_gift_id] = {
                "name": name,
                "keys": keys,
                "sound": sound_var.get(),
                "media": media_filename if media_path else "",
                "trigger_type": trigger_type,
                "trigger_value": trigger_value,
                "hide_media": hide_media,
                "duration": duration
            }

            # حفظ التغييرات في الملف
            self.save_key_bindings()

            # إغلاق النافذة
            dialog.destroy()
            self.log_comment(
                f"✅ تم حفظ محاكاة المفتاح للهدية: {selected_gift_name} (ID: {selected_gift_id})"
            )
            if self.media_url:
                self.log_comment(f"رابط الوسائط: {self.media_url}")

        # أزرار التحكم
        buttons_frame = ttk.Frame(main_frame, style="Dialog.TFrame")
        buttons_frame.pack(fill="x", pady=10)

        test_keys_btn = ModernButton(
            buttons_frame,
            text="🔍 اختبار المفاتيح",
            width=12,
            command=test_keys,
            style_name="Modern.TButton",
        )
        test_keys_btn.pack(side="left", padx=2)

        test_sound_btn = ModernButton(
            buttons_frame,
            text="🔊 اختبار الصوت",
            width=12,
            command=test_sound,
            style_name="Modern.TButton",
        )
        test_sound_btn.pack(side="left", padx=2)

        test_media_btn = ModernButton(
            buttons_frame,
            text="🎬 تجربة الوسائط",
            width=12,
            command=test_media,
            style_name="Modern.TButton",
        )
        test_media_btn.pack(side="left", padx=2)

        save_button = ModernButton(
            buttons_frame,
            text="💾 حفظ",
            width=8,
            command=save,
            style_name="Modern.TButton",
        )
        save_button.pack(side="right", padx=2)

        # جعل النافذة مركزية
        dialog.transient(self.root)
        dialog.grab_set()
        self.root.wait_window(dialog)

    def toggle_connection(self):
        if not self.is_connected:
            self.connect_to_live()
        else:
            self.disconnect_from_live()

    def connect_to_live(self):
        username = self.username_var.get().strip()
        if not username:
            self.log_comment("الرجاء إدخال اسم مستخدم TikTok")
            return

        if not username.startswith("@"):
            username = f"@{username}"

        self.status_var.set("الحالة: جاري الاتصال...")
        self.connect_btn.configure(state="disabled")

        threading.Thread(
            target=self.start_client, args=(username,), daemon=True
        ).start()

    def disconnect_from_live(self):
        if self.client:
            try:
                # تعطيل الزر وتحديث الحالة
                self.connect_btn.configure(state="disabled", text="جاري قطع الاتصال...")
                self.status_var.set("الحالة: جاري قطع الاتصال...")

                # إيقاف جميع المستمعين
                self.client.remove_all_listeners()

                # إيقاف اتصال WebSocket
                if hasattr(self.client, "_websocket") and self.client._websocket:
                    try:
                        self.client._websocket.close()
                    except BaseException:
                        pass

                # إيقاف العميل وتنظيف الموارد
                if hasattr(self.client, "_room"):
                    self.client._room = None
                if hasattr(self.client, "_connect_id"):
                    self.client._connect_id = None

                # تعيين حالة التوقف
                self.client._stopped = True
                self.client = None
                self.is_connected = False

                # تحديث الواجهة
                self.status_var.set("الحالة: تم قطع الاتصال")
                self.connect_btn.configure(text="اتصال", state="normal")
                self.log_comment("تم قطع الاتصال بنجاح")

                # تشغيل صوت قطع الاتصال
                if os.path.exists(self.disconnect_sound):
                    threading.Thread(
                        target=playsound, args=(self.disconnect_sound,), daemon=True
                    ).start()

            except Exception as e:
                self.log_comment(f"خطأ في قطع الاتصال: {str(e)}")
                self.connect_btn.configure(text="اتصال", state="normal")
                self.status_var.set("الحالة: غير متصل")
                self.is_connected = False

    def start_client(self, username):
        try:
            self.client = TikTokLiveClient(unique_id=username)

            @self.client.on(ConnectEvent)
            async def on_connect(event):
                self.is_connected = True
                self.root.after(
                    0,
                    lambda: [
                        self.status_var.set(f"الحالة: متصل بـ {event.unique_id}"),
                        self.connect_btn.configure(text="قطع الاتصال", state="normal"),
                        self.log_comment(f"تم الاتصال بـ {event.unique_id}!"),
                    ],
                )

                # تشغيل صوت الاتصال
                if os.path.exists(self.connect_sound):
                    threading.Thread(
                        target=playsound, args=(self.connect_sound,), daemon=True
                    ).start()

            @self.client.on(DisconnectEvent)
            async def on_disconnect(_):
                self.root.after(
                    0,
                    lambda: [
                        self.status_var.set("الحالة: تم قطع الاتصال"),
                        self.connect_btn.configure(text="اتصال", state="normal"),
                        self.log_comment("تم قطع الاتصال من الخادم"),
                    ],
                )
                self.is_connected = False

                # محاولة إعادة الاتصال تلقائياً
                if self.reconnect_attempts < self.max_reconnect_attempts:
                    self.reconnect_attempts += 1
                    self.log_comment(
                        f"محاولة إعادة الاتصال {self.reconnect_attempts}/{self.max_reconnect_attempts}"
                    )

                    # تشغيل صوت فشل الاتصال
                    if os.path.exists(self.connection_error_sound):
                        threading.Thread(
                            target=playsound,
                            args=(self.connection_error_sound,),
                            daemon=True,
                        ).start()

                    # محاولة إعادة الاتصال بعد 5 ثوانٍ
                    self.root.after(5000, lambda: self.toggle_connection())
                else:
                    self.reconnect_attempts = 0
                    self.log_comment("فشلت جميع محاولات إعادة الاتصال")

                # تشغيل صوت قطع الاتصال
                if os.path.exists(self.disconnect_sound):
                    threading.Thread(
                        target=playsound, args=(self.disconnect_sound,), daemon=True
                    ).start()

            @self.client.on(CommentEvent)
            async def on_comment(event):
                try:
                    # تنسيق رسالة التعليق
                    current_time = datetime.now().strftime("%H:%M:%S")
                    message = (
                        f"[{current_time}] 💭 {event.user.nickname}: {event.comment}"
                    )
                    self.log_comment(message)
                except Exception as e:
                    self.log_comment(f"خطأ في معالجة التعليق: {str(e)}")

            @self.client.on(GiftEvent)
            async def on_gift(event):
                try:
                    gift_id = str(event.gift.id)  # تحويل معرف الهدية إلى نص

                    # التحقق من نوع الهدية وإمكانية تكرارها
                    if hasattr(event.gift, "streakable"):
                        # هدية قابلة للتكرار - ننتظر حتى تنتهي السلسلة
                        if event.gift.streakable and event.streaking:
                            return  # تجاهل الهدايا المتوسطة في السلسلة

                        # نهاية السلسلة أو هدية فردية
                        gift_count = (
                            event.repeat_count if hasattr(event, "repeat_count") else 1
                        )
                    else:
                        # التعامل مع البروتوكول القديم
                        if hasattr(event.gift, "info"):
                            if event.gift.info.type == 1:
                                if (
                                    not hasattr(event, "repeat_end")
                                    or not event.repeat_end
                                ):
                                    return  # تجاهل الهدايا المتوسطة في السلسلة
                                gift_count = (
                                    event.gift.repeat_count
                                    if hasattr(event.gift, "repeat_count")
                                    else 1
                                )
                            else:
                                gift_count = 1
                        else:
                            gift_count = 1

                    # التحقق من وجود معرف الهدية في الإعدادات
                    if gift_id in self.key_bindings:
                        gift_info = self.gifts_data.get(gift_id, {})
                        gift_name = gift_info.get("name", "هدية غير معروفة")

                        # تسجيل الهدية في منطقة الهدايا
                        current_time = datetime.now().strftime("%H:%M:%S")
                        message = f"[{current_time}] 🎁 {event.user.nickname} أرسل {gift_name} × {gift_count}"
                        self.gifts_queue.put(message)

                        # تنفيذ المحاكاة بشكل متتابع عند استلام هدية مكررة
                        def execute_step(index=0):
                            if index >= gift_count:
                                return  # إنهاء التنفيذ بعد العدد المطلوب

                            print(
                                f"🔄 تنفيذ {index + 1} من {gift_count}"
                            )  # لتتبع التنفيذ
                            # تشغيل محاكاة المفاتيح
                            self.simulate_keys(gift_id)

                            # تأخير التنفيذ التالي بـ 500 مللي ثانية
                            self.root.after(500, lambda: execute_step(index + 1))

                    execute_step()  # بدء التنفيذ التدريجي

                except Exception as e:
                    error_message = f"❌ خطأ في معالجة الهدية: {str(e)}"
                    self.gifts_queue.put(error_message)

            @self.client.on(JoinEvent)
            async def on_join(event):
                for gift_id, binding in self.key_bindings.items():
                    if binding.get("trigger_type") == "join" and binding.get("trigger_value") == event.user.nickname:
                        self.simulate_keys(gift_id)

            @self.client.on(LikeEvent)
            async def on_like(event):
                for gift_id, binding in self.key_bindings.items():
                    if binding.get("trigger_type") == "likes":
                        try:
                            likes_threshold = int(binding.get("trigger_value", 0))
                            if event.total_likes >= likes_threshold:
                                self.simulate_keys(gift_id)
                        except ValueError:
                            pass

            @self.client.on(FollowEvent)
            async def on_follow(event):
                for gift_id, binding in self.key_bindings.items():
                    if binding.get("trigger_type") == "follow" and binding.get("trigger_value") == event.user.nickname:
                        self.simulate_keys(gift_id)

            self.client.run()

        except Exception as e:
            self.is_connected = False
            self.reconnect_attempts += 1  # زيادة عدد المحاولات
            error_message = (
                f"فشل الاتصال: {str(e)} (محاولة رقم {self.reconnect_attempts})"
            )

            self.root.after(
                0,
                lambda: [
                    self.status_var.set("الحالة: فشل الاتصال"),
                    self.connect_btn.configure(state="normal"),
                    self.log_comment(error_message),
                ],
            )

            # تشغيل صوت فشل الاتصال إن وجد
            if os.path.exists(self.connection_error_sound):
                threading.Thread(
                    target=playsound, args=(self.connection_error_sound,), daemon=True
                ).start()

            # محاولة إعادة الاتصال بعد 5 ثوانٍ
            self.root.after(5000, lambda: self.connect_to_live())

    def save_settings(self):
        """حفظ الإعدادات"""
        if self.current_profile:
            self.profiles[self.current_profile]["bindings"] = self.key_bindings
        self.save_profiles()

    def update_keys_display(self):
        """تحديث عرض المفاتيح في الواجهة"""
        # تحديث حقل المحاكاة
        self.simulation_text.delete("1.0", tk.END)
        for gift_id, binding in self.key_bindings.items():
            gift_name = self.gifts_data.get(gift_id, {}).get("name", gift_id)
            custom_name = binding.get("name", "")  # استخدم المفتاح الصحيح
            keys = binding.get("keys", "")
            sound = binding.get("sound", "")

            # استخراج اسم الملف فقط من مسار الصوت
            if sound:
                sound = os.path.basename(sound)

            # بناء النص للعرض
            display_text = f"🎁 {gift_name}"
            if custom_name:
                display_text += f" ({custom_name})"
            display_text += f" | ⌨️ {keys}"
            if sound:
                display_text += f" | 🔊 {sound}"
            display_text += "\n"

            self.simulation_text.insert(tk.END, display_text)

    def update_keys_from_text(self):
        """تحديث المفاتيح من النص المعروض"""
        try:
            text = self.keys_text.get("1.0", tk.END).strip()
            new_bindings = {}

            for line in text.split("\n"):
                if not line.strip():
                    continue

                try:
                    # تقسيم السطر إلى أجزاء
                    parts = [p.strip() for p in line.split("|")]
                    if len(parts) < 2:
                        continue

                    # استخراج اسم الهدية
                    gift_name = parts[0].replace("الهدية:", "").strip()

                    # استخراج المفاتيح
                    keys_part = next((p for p in parts if "المفاتيح:" in p), None)
                    if not keys_part:
                        continue
                    keys = [
                        k.strip() for k in keys_part.replace("المفاتيح:", "").split(",")
                    ]

                    # استخراج الصوت (إذا وجد)
                    sound_part = next((p for p in parts if "الصوت:" in p), None)
                    sound = (
                        sound_part.replace("الصوت:", "").strip() if sound_part else None
                    )

                    # استخراج الاسم (إذا وجد)
                    name_part = next((p for p in parts if "الاسم:" in p), None)
                    name = (
                        name_part.replace("الاسم:", "").strip() if name_part else None
                    )

                    # تخزين البيانات
                    if sound:
                        new_bindings[gift_id] = {
                            "keys": keys,
                            "sound": (
                                sound if sound else ""
                            ),  # ضمان وجود الصوت حتى لو لم يتم تعيينه
                            "name": gift_name,  # الاحتفاظ باسم الهدية للاستخدام البصري
                        }

                except Exception as e:
                    self.log_comment(f"❌ خطأ في معالجة السطر '{line}': {str(e)}")
                    continue

            # تحديث المفاتيح
            self.key_bindings = new_bindings

        except Exception as e:
            self.log_comment(f"❌ خطأ في تحديث المفاتيح: {str(e)}")

    def log_comment(self, message):
        """تسجيل التعليق وقراءته"""
        # إضافة التعليق للعرض
        self.comments_queue.put(message)

        if not self.tts_enabled:
            return

        try:
            # استخراج نص التعليق فقط
            comment_match = re.search(r"\[(.*?)\] 💭 (.*?): (.*)", message)
            if comment_match:
                _, username, comment = comment_match.groups()
                if self.tts_language == "ar":
                    text_to_speak = f"{username} يقول: {comment}"
                else:
                    text_to_speak = f"{username} says: {comment}"

                # إضافة التعليق لقائمة القراءة
                self.tts_queue.put(text_to_speak)

        except Exception as e:
            print(f"خطأ في معالجة التعليق: {str(e)}")

    def log_gift(self, message):
        self.gifts_queue.put(message)

    def process_messages(self):
        """معالجة الرسائل والهدايا من قوائم الانتظار"""
        try:
            # معالجة التعليقات للعرض
            while not self.comments_queue.empty():
                message = self.comments_queue.get()
                self.comments_area.configure(state="normal")
                self.comments_area.insert("1.0", message + "\n")
                self.comments_area.configure(state="disabled")
                self.comments_area.see("1.0")

            # معالجة قراءة التعليقات
            if not self.tts_queue.empty() and not self.is_speaking:
                text = self.tts_queue.get()
                self.is_speaking = True
                self.speak_text(text)
                self.is_speaking = False

            # معالجة الهدايا
            while not self.gifts_queue.empty():
                message = self.gifts_queue.get()
                self.gifts_area.configure(state="normal")
                self.gifts_area.insert("1.0", message + "\n")
                self.gifts_area.configure(state="disabled")
                self.gifts_area.see("1.0")

        except Exception as e:
            print(f"خطأ في معالجة الرسائل: {str(e)}")

        # جدولة المعالجة التالية
        self.root.after(50, self.process_messages)

    def simulate_keys(self, gift_id):
        """محاكاة ضغط المفاتيح للهدية المحددة"""
        if gift_id not in self.key_bindings:
            return

        binding = self.key_bindings[gift_id]
        keys_text = binding.get("keys", "")
        sound = binding.get("sound")
        media = binding.get("media")
        duration = binding.get("duration", 5000)

        # تشغيل الصوت إذا كان موجوداً
        if sound and os.path.exists(sound):
            threading.Thread(target=playsound, args=(sound,), daemon=True).start()

        # عرض الوسائط إذا كانت موجودة
        if media and self.media_url:
            self.show_media(media, duration)

        # تقسيم النص إلى تسلسلات
        key_sequences = [k.strip() for k in keys_text.split(";")]

        # محاكاة المفاتيح بشكل متسلسل
        def simulate_sequence(index=0):
            if index >= len(key_sequences):
                self.log_comment("✅ تم محاكاة المفاتيح بنجاح!")
                return

            sequence = key_sequences[index]
            try:
                # استخراج المفتاح والتأخير
                if "@" in sequence:
                    key_part, delay = sequence.split("@")
                    delay = float(delay)
                else:
                    key_part = sequence
                    delay = 100  # تأخير افتراضي 100 مللي ثانية

                # معالجة المفاتيح المركبة
                if "+" in key_part:
                    key_parts = [k.strip().lower() for k in key_part.split("+")]
                    # الضغط على جميع المفاتيح
                    for k in key_parts[:-1]:
                        keyboard.press(k)
                    # الضغط والتحرير للمفتاح الأخير
                    keyboard.press_and_release(key_parts[-1])
                    # تحرير جميع المفاتيح
                    for k in reversed(key_parts[:-1]):
                        keyboard.release(k)
                else:
                    # مفتاح واحد
                    keyboard.press_and_release(key_part.lower())

                # جدولة التسلسل التالي
                self.root.after(int(delay), lambda: simulate_sequence(index + 1))

            except Exception as e:
                self.log_comment(f"❌ خطأ في محاكاة المفتاح {sequence}: {str(e)}")
                return

        # بدء محاكاة المفاتيح
        simulate_sequence()

    def toggle_tts(self):
        """تفعيل/تعطيل قراءة التعليقات"""
        self.tts_enabled = not self.tts_enabled
        if self.tts_enabled:
            self.tts_button.config(text="تعطيل القراءة", style="Danger.TButton")
        else:
            self.tts_button.config(text="تفعيل القراءة", style="Success.TButton")

    def update_tts_language(self):
        """تحديث لغة قراءة التعليقات"""
        self.tts_language = self.language_var.get()
        self.update_voice_list()  # تحديث قائمة الأصوات عند تغيير اللغة

    def update_voice_list(self):
        """تحديث قائمة الأصوات المتاحة"""
        voices = self.voices[self.tts_language]

        # تحديث القائمة المنسدلة بأسماء الأصوات
        self.voice_menu["values"] = list(voices.values())

        # تعيين الصوت الافتراضي
        default_voice = (
            "ar-EG-ShakirNeural"
            if self.tts_language == "ar"
            else "en-US-ChristopherNeural"
        )
        self.selected_voice.set(default_voice)

        # عرض الاسم المقابل للصوت الافتراضي
        self.voice_menu.set(voices[default_voice])

        # للتشخيص
        print(f"تم تحديث قائمة الأصوات. الصوت الافتراضي: {default_voice}")

        # ربط حدث تغيير القيمة
        self.voice_menu.bind("<<ComboboxSelected>>", self.on_voice_selected)

    def on_voice_selected(self, event):
        """معالجة اختيار صوت جديد"""
        display_name = self.voice_menu.get()

        # البحث عن معرف الصوت المقابل للاسم المعروض
        for voice_id, name in self.voices[self.tts_language].items():
            if name == display_name:
                self.selected_voice.set(voice_id)
                print(f"تم اختيار الصوت: {voice_id}")  # للتشخيص
                break

    def browse_sound_file(self, sound_var):
        """اختيار ملف صوت"""
        file_path = filedialog.askopenfilename(
            title="اختر ملف الصوت",
            filetypes=[("ملفات الصوت", "*.wav;*.mp3"), ("جميع الملفات", "*.*")],
        )
        if file_path:
            try:
                # منح المستخدم خيار حفظ المسار كمسار مطلق أو نسخ الملف إلى مجلد
                # الأصوات
                choice = messagebox.askyesno(
                    "حفظ المسار",
                    "هل ترغب في حفظ المسار المطلق للملف؟\n\n"
                    "نعم = حفظ المسار المطلق\nلا = نسخ الملف إلى مجلد الأصوات",
                )

                if choice:
                    # حفظ المسار المطلق
                    sound_var.set(file_path)
                    self.log_comment(f"✅ تم حفظ المسار المطلق: {file_path}")
                else:
                    # نسخ الملف إلى مجلد الأصوات
                    if not os.path.exists(self.sounds_dir):
                        os.makedirs(self.sounds_dir)

                    sound_filename = os.path.basename(file_path)
                    target_path = os.path.join(self.sounds_dir, sound_filename)

                    if not os.path.exists(target_path):
                        import shutil

                        shutil.copy2(file_path, target_path)

                    # حفظ المسار النسبي
                    relative_path = os.path.join("sounds", sound_filename)
                    sound_var.set(relative_path)
                    self.log_comment(f"✅ تم نسخ الملف إلى: {relative_path}")
            except Exception as e:
                self.log_comment(f"❌ خطأ في اختيار ملف الصوت: {str(e)}")

    def browse_media_file(self, media_var):
        """اختيار ملف وسائط"""
        file_path = filedialog.askopenfilename(
            title="اختر ملف الوسائط",
            filetypes=[
                ("ملفات الوسائط", "*.mp4;*.webm;*.gif;*.png;*.jpg;*.jpeg;*.webp"),
                ("جميع الملفات", "*.*")
            ],
        )
        if file_path:
            media_var.set(file_path)
            self.log_comment(f"✅ تم اختيار الوسائط: {file_path}")

    def delete_selected_key(self):
        """حذف المفتاح المحدد"""
        try:
            selected_text = self.simulation_text.get("sel.first", "sel.last")
            if not selected_text:
                self.log_comment("⚠️ الرجاء تحديد المفتاح المراد حذفه")
                return

            # استخراج معرف الهدية من النص المحدد
            gift_id = None
            for gid in self.key_bindings:
                gift_name = self.gifts_data.get(gid, {}).get("name", gid)
                if gift_name in selected_text:
                    gift_id = gid
                    break

            if gift_id and gift_id in self.key_bindings:
                del self.key_bindings[gift_id]
                self.update_keys_display()
                self.log_comment("✅ تم حذف المفتاح بنجاح")
            else:
                self.log_comment("⚠️ لم يتم العثور على المفتاح المحدد")
        except BaseException:
            self.log_comment("⚠️ الرجاء تحديد المفتاح المراد حذفه")

    def edit_selected_key(self):
        """تعديل المفتاح المحدد"""
        try:
            # الحصول على النص المحدد
            selected_text = self.simulation_text.get("sel.first", "sel.last")
            if not selected_text:
                self.log_comment("⚠️ الرجاء تحديد المفتاح المراد تعديله")
                return

            # البحث عن الهدية في النص المحدد
            gift_id = None
            for gid in self.key_bindings:
                gift_name = self.gifts_data.get(gid, {}).get("name", gid)
                if gift_name in selected_text:
                    gift_id = gid
                    break

            if not gift_id:
                self.log_comment("⚠️ لم يتم العثور على المفتاح المحدد")
                return

            # الحصول على بيانات المفتاح
            binding = self.key_bindings[gift_id]

            # إنشاء نافذة التعديل
            dialog = tk.Toplevel(self.root)
            dialog.title("تعديل محاكاة المفتاح")
            dialog.geometry("400x300")
            dialog.configure(bg="#1e1e1e")

            # الإطار الرئيسي
            frame = ttk.Frame(dialog, style="Custom.TFrame", padding="20")
            frame.pack(fill="both", expand=True)

            # حقل الهدية
            ttk.Label(frame, text="الهدية:", style="Custom.TLabel").pack(
                fill="x", pady=(0, 5)
            )
            gift_label = ttk.Label(
                frame,
                text=self.gifts_data.get(gift_id, {}).get("name", gift_id),
                style="Custom.TLabel",
            )
            gift_label.pack(fill="x", pady=(0, 10))

            # حقل المفاتيح
            ttk.Label(frame, text="المفاتيح:", style="Custom.TLabel").pack(
                fill="x", pady=(0, 5)
            )
            keys_entry = ttk.Entry(frame, style="Custom.TEntry")
            keys_entry.insert(0, binding.get("keys", ""))
            keys_entry.pack(fill="x", pady=(0, 10))

            # زر اختبار المفاتيح
            test_result_label = ttk.Label(frame, text="", style="Custom.TLabel")
            test_result_label.pack(fill="x", pady=(0, 10))

            def test_keys():
                success, message = self.test_keys(keys_entry.get())
                test_result_label.config(text=message)
                return success

            ModernButton(frame, text="اختبار المفاتيح", command=test_keys).pack(
                fill="x", pady=(0, 10)
            )

            # حقل الصوت
            ttk.Label(frame, text="الصوت:", style="Custom.TLabel").pack(
                fill="x", pady=(0, 5)
            )
            sound_entry = ttk.Entry(frame, style="Custom.TEntry")
            sound_entry.insert(0, binding.get("sound", ""))
            sound_entry.pack(fill="x", pady=(0, 10))

            def save_changes():
                new_keys = keys_entry.get().strip()
                new_sound = sound_entry.get().strip()

                if not new_keys:
                    self.log_comment("⚠️ الرجاء ملء حقل المفاتيح")
                    return

                # التحقق من صحة المفاتيح
                if not test_keys():
                    return

                # تحديث المفتاح
                self.key_bindings[gift_id] = {"keys": new_keys, "sound": new_sound}

                # تحديث العرض
                self.update_keys_display()
                self.save_profiles()

                dialog.destroy()
                self.log_comment(
                    f"✅ تم تعديل المفتاح: {self.gifts_data.get(gift_id, {}).get('name', gift_id)}"
                )

            # أزرار الحفظ والإلغاء
            buttons_frame = ttk.Frame(frame, style="Custom.TFrame")
            buttons_frame.pack(fill="x", pady=(10, 0))

            ModernButton(buttons_frame, text="حفظ", command=save_changes).pack(
                side="left", padx=5
            )
            ModernButton(buttons_frame, text="إلغاء", command=dialog.destroy).pack(
                side="left"
            )

            # جعل النافذة مركزية
            dialog.transient(self.root)
            dialog.grab_set()
            dialog.focus()

        except Exception as e:
            self.log_comment(f"❌ خطأ في تعديل المفتاح: {str(e)}")

    def save_profile(self):
        """حفظ البروفايل الحالي"""
        if not self.current_profile:
            # فتح نافذة لإدخال اسم البروفايل
            dialog = tk.Toplevel(self.root)
            dialog.title("حفظ البروفايل")
            dialog.geometry("300x150")
            dialog.resizable(False, False)
            dialog.configure(bg="#1e1e1e")  # إضافة خلفية داكنة

            # إطار الإدخال
            frame = ttk.Frame(dialog, padding="20")
            frame.pack(fill="both", expand=True)

            ttk.Label(frame, text="اسم البروفايل:").pack(pady=(0, 10))

            name_var = tk.StringVar()
            name_entry = ttk.Entry(frame, textvariable=name_var)
            name_entry.pack(fill="x", pady=(0, 20))
            name_entry.focus()

            def save():
                name = name_var.get().strip()
                if name:
                    # حفظ البروفايل
                    self.profiles[name] = {
                        "bindings": self.key_bindings.copy(),
                        "created_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    }
                    self.current_profile = name

                    # تحديث الواجهة
                    self.update_profiles_list()
                    self.save_profiles()

                    dialog.destroy()
                    self.log_comment(f"✅ تم حفظ البروفايل: {name}")
                else:
                    self.log_comment("❌ الرجاء إدخال اسم للبروفايل")

            # أزرار التحكم
            buttons_frame = ttk.Frame(frame)
            buttons_frame.pack(fill="x")

            ttk.Button(buttons_frame, text="حفظ", command=save).pack(
                side="right", padx=5
            )
            ttk.Button(buttons_frame, text="إلغاء", command=dialog.destroy).pack(
                side="right"
            )

            # ربط مفتاح Enter بزر الحفظ
            dialog.bind("<Return>", lambda e: save())
            dialog.bind("<Escape>", lambda e: dialog.destroy())

            # جعل النافذة مركزية
            dialog.transient(self.root)
            dialog.grab_set()
            self.root.wait_window(dialog)
        else:
            # تحديث البروفايل الحالي
            self.profiles[self.current_profile] = {
                "bindings": self.key_bindings.copy(),
                "updated_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            }
            self.save_profiles()
            self.log_comment(f"✅ تم تحديث البروفايل: {self.current_profile}")

    def update_profile_name(self):
        """تحديث اسم البروفايل المعروض"""
        try:
            # تحديث عنوان النافذة
            title = "StreamTok"
            if self.current_profile:
                title += f" - {self.current_profile}"
            self.root.title(title)

            # تحديث حقل اسم البروفايل إذا كان موجوداً
            if hasattr(self, "profile_name_var"):
                self.profile_name_var.set(self.current_profile or "")
        except Exception as e:
            self.log_comment(f"❌ خطأ في تحديث اسم البروفايل: {str(e)}")

    def __del__(self):
        """تنظيف الملفات المؤقتة وإغلاق حلقة الأحداث عند إغلاق البرنامج"""
        try:
            if hasattr(self, "loop") and self.loop.is_running():
                self.loop.stop()
            if hasattr(self, "loop") and not self.loop.is_closed():
                self.loop.close()

            if hasattr(self, "temp_dir") and os.path.exists(self.temp_dir):
                for file in os.listdir(self.temp_dir):
                    try:
                        os.remove(os.path.join(self.temp_dir, file))
                    except BaseException:
                        pass
                os.rmdir(self.temp_dir)
        except BaseException:
            pass

    def speak_text(self, text):
        """قراءة النص بالصوت"""
        if not self.tts_enabled:
            return

        try:
            # تنظيف النص من الرموز التعبيرية
            clean_text = re.sub(r"[😊🎁💎✨⚡❌✅💭]", "", text)

            if self.tts_language == "ar":
                # ترجمة الأرقام إلى كلمات عربية
                clean_text = self.convert_numbers_to_arabic_words(clean_text)

            print(f"جاري قراءة النص: {clean_text}")  # للتشخيص

            # تشغيل الصوت في خيط منفصل
            threading.Thread(
                target=self._run_async_speak, args=(clean_text,), daemon=True
            ).start()

        except Exception as e:
            print(f"خطأ في قراءة النص: {str(e)}")
            traceback.print_exc()

    def convert_numbers_to_arabic_words(self, text):
        """تحويل الأرقام إلى كلمات عربية"""
        # قاموس الأرقام العربية
        numbers = {
            "0": "صفر",
            "1": "واحد",
            "2": "اثنين",
            "3": "ثلاثة",
            "4": "أربعة",
            "5": "خمسة",
            "6": "ستة",
            "7": "سبعة",
            "8": "ثمانية",
            "9": "تسعة",
        }

        # استبدال كل رقم بما يقابله من كلمات
        for num, word in numbers.items():
            text = text.replace(num, f" {word} ")

        return text

    def _run_async_speak(self, text):
        """تشغيل قراءة النص بشكل غير متزامن"""
        try:
            voice_id = self.selected_voice.get()
            print(f"معرف الصوت المستخدم: {voice_id}")  # للتشخيص

            # التحقق من صحة معرف الصوت
            if voice_id not in self.voices[self.tts_language]:
                # إذا كان المعرف غير صالح، استخدم الصوت الافتراضي
                voice_id = (
                    "ar-EG-ShakirNeural"
                    if self.tts_language == "ar"
                    else "en-US-ChristopherNeural"
                )
                self.selected_voice.set(voice_id)
                print(f"تم التحويل إلى الصوت الافتراضي: {voice_id}")

            communicate = edge_tts.Communicate(text, voice_id)
            temp_file = os.path.join(self.temp_dir, f"speech_{int(time.time())}.mp3")

            # استخدام asyncio بشكل صحيح
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            try:
                # حفظ الصوت في ملف مؤقت
                loop.run_until_complete(communicate.save(temp_file))
                print(f"تم حفظ الصوت في: {temp_file}")  # للتشخيص

                # تشغيل الصوت وانتظار انتهائه
                playsound(temp_file, block=True)
                print("تم تشغيل الصوت")  # للتشخيص

                # حذف الملف المؤقت
                if os.path.exists(temp_file):
                    os.remove(temp_file)

            finally:
                loop.close()
                self.is_speaking = False

        except Exception as e:
            print(f"خطأ في تشغيل الصوت: {str(e)}")
            self.is_speaking = False
            traceback.print_exc()

    def cleanup(self):
        """تنظيف الموارد عند إغلاق التطبيق"""
        try:
            if hasattr(self, "client"):
                self.client.stop()

            if hasattr(self, "loop") and self.loop:
                self.loop.close()

            if hasattr(self, "temp_dir") and os.path.exists(self.temp_dir):
                for file in os.listdir(self.temp_dir):
                    try:
                        os.remove(os.path.join(self.temp_dir, file))
                    except BaseException:
                        pass
                os.rmdir(self.temp_dir)
        except BaseException:
            pass

    def save_key_bindings(self):
        """حفظ ربط المفاتيح في ملف"""
        try:
            # التأكد من وجود المجلد
            if not os.path.exists("config"):
                os.makedirs("config")

            # حفظ البيانات في ملف
            with open("config/key_bindings.json", "w", encoding="utf-8") as f:
                json.dump(self.key_bindings, f, ensure_ascii=False, indent=4)

            # تحديث العرض
            self.update_keys_display()

            self.log_comment("✅ تم حفظ ربط المفاتيح بنجاح")
        except Exception as e:
            self.log_comment(f"❌ خطأ في حفظ ربط المفاتيح: {str(e)}")

    def load_key_bindings(self):
        """تحميل ربط المفاتيح من ملف"""
        try:
            if os.path.exists("config/key_bindings.json"):
                with open("config/key_bindings.json", "r", encoding="utf-8") as f:
                    self.key_bindings = json.load(f)

                # تحديث العرض بعد التحميل
                self.update_keys_display()

                self.log_comment("✅ تم تحميل ربط المفاتيح بنجاح")
            else:
                self.key_bindings = {}
                self.log_comment("ℹ️ لم يتم العثور على ملف ربط المفاتيح")
        except Exception as e:
            self.log_comment(f"❌ خطأ في تحميل ربط المفاتيح: {str(e)}")

    def load_profile(self, event=None):
        """تحميل البروفايل المحدد"""
        try:
            # الحصول على البروفايل المحدد
            selection = self.profiles_list.curselection()
            if not selection:
                self.log_comment("⚠️ الرجاء تحديد بروفايل")
                return

            # الحصول على اسم البروفايل
            name = self.profiles_list.get(selection[0])

            if name in self.profiles:
                # تحميل البيانات
                self.key_bindings = self.profiles[name]["bindings"]
                self.current_profile = name

                # تحديث العرض
                self.update_keys_display()
                self.update_profile_name()

                self.log_comment(f"✅ تم تحميل البروفايل: {name}")
            else:
                self.log_comment("⚠️ لم يتم العثور على البروفايل")
        except Exception as e:
            self.log_comment(f"❌ خطأ في تحميل البروفايل: {str(e)}")

    def update_profiles_list(self):
        """تحديث قائمة البروفايلات"""
        try:
            # مسح القائمة
            self.profiles_list.delete(0, tk.END)

            # إضافة البروفايلات
            for name in sorted(self.profiles.keys()):
                self.profiles_list.insert(tk.END, name)

            # تحديد البروفايل الحالي إذا وجد
            if self.current_profile:
                try:
                    index = sorted(self.profiles.keys()).index(self.current_profile)
                    self.profiles_list.selection_set(index)
                except ValueError:
                    pass

        except Exception as e:
            self.log_comment(f"❌ خطأ في تحديث قائمة البروفايلات: {str(e)}")

    def test_keys(self, keys_text):
        """اختبار محاكاة المفاتيح"""
        try:
            # تقسيم النص إلى مفاتيح
            key_sequences = [k.strip() for k in keys_text.split(";")]

            for key_sequence in key_sequences:
                if not key_sequence:
                    continue

                # تقسيم التسلسل إلى خطوات
                steps = [step.strip() for step in key_sequence.split(";")]

                for step in steps:
                    if not step:
                        continue

                    # معالجة التأخير والمفتاح
                    parts = step.split("@")
                    key_part = parts[0].strip()

                    # تنفيذ التأخير قبل الضغط
                    if len(parts) > 1:
                        try:
                            delay = int(parts[1])
                            if delay > 0:
                                time.sleep(delay / 1000)
                        except ValueError:
                            pass

                    # معالجة المفاتيح المركبة
                    if "+" in key_part:
                        key_parts = [k.strip().lower() for k in key_part.split("+")]
                        # الضغط على جميع المفاتيح
                        for k in key_parts[:-1]:
                            keyboard.press(k)
                        # الضغط والتحرير للمفتاح الأخير
                        keyboard.press_and_release(key_parts[-1])
                        # تحرير جميع المفاتيح
                        for k in reversed(key_parts[:-1]):
                            keyboard.release(k)
                    else:
                        # مفتاح واحد
                        keyboard.press_and_release(key_part.lower())

            return True, "✅ تم اختبار المفاتيح بنجاح"

        except Exception as e:
            return False, f"❌ خطأ في اختبار المفاتيح: {str(e)}"

    def on_profile_selected(self):
        """معالجة حدث اختيار بروفايل"""
        selected = self.profiles_list.get()
        if selected:
            self.current_profile = selected
            self.key_bindings = self.profiles[selected]["bindings"]
            self.update_keys_display()
            self.update_profile_name()
            self.log_comment(f"✅ تم تحميل البروفايل: {selected}")

    def delete_profile(self):
        """حذف البروفايل الحالي"""
        selected = self.profiles_list.get()
        if not selected:
            self.log_comment("⚠️ الرجاء اختيار بروفايل للحذف")
            return

        if messagebox.askyesno(
            "تأكيد الحذف", f"هل أنت متأكد من حذف البروفايل '{selected}'؟"
        ):
            # حذف البروفايل
            del self.profiles[selected]

            # إعادة تعيين البروفايل الحالي إذا تم حذفه
            if self.current_profile == selected:
                self.current_profile = None
                self.key_bindings = {}
                self.update_keys_display()
                self.update_profile_name()

            # تحديث القائمة
            self.update_profiles_list()
            self.save_profiles()

            self.log_comment(f"✅ تم حذف البروفايل: {selected}")

    def test_sound(self, sound_file):
        """اختبار تشغيل الصوت"""
        try:
            if not sound_file:
                return False, "⚠️ الرجاء اختيار ملف صوتي"

            # التحقق من وجود الملف الصوتي
            if not os.path.exists(sound_file):
                return False, "⚠️ ملف الصوت غير موجود"

            # تشغيل الصوت في خيط منفصل
            threading.Thread(target=playsound, args=(sound_file,), daemon=True).start()
            return True, "✅ تم تشغيل الصوت بنجاح"

        except Exception as e:
            return False, f"❌ خطأ في تشغيل الصوت: {str(e)}"

    def manage_profiles(self):
        """إدارة البروفايلات"""
        dialog = tk.Toplevel(self.root)
        dialog.title("إدارة البروفايلات")
        dialog.geometry("600x500")
        dialog.configure(bg="#1e1e1e")

        # الإطار الرئيسي
        main_frame = ttk.Frame(dialog, style="Modern.TFrame", padding="20")
        main_frame.pack(fill="both", expand=True)

        # إطار معلومات البروفايل الحالي
        current_profile_frame = ttk.LabelFrame(
            main_frame,
            text="البروفايل الحالي",
            style="Modern.TLabelframe",
            padding="10",
        )
        current_profile_frame.pack(fill="x", pady=(0, 10))

        # عرض اسم البروفايل الحالي
        current_profile_label = ttk.Label(
            current_profile_frame,
            text=f"البروفايل النشط: {self.current_profile if self.current_profile else 'لا يوجد'}",
            style="Modern.TLabel",
        )
        current_profile_label.pack(fill="x", padx=5, pady=5)

        # عدد المفاتيح في البروفايل الحالي
        keys_count = 0
        if self.current_profile and self.current_profile in self.profiles:
            keys_count = len(self.profiles[self.current_profile].get("bindings", {}))

        self.keys_label = ttk.Label(
            current_profile_frame,
            text=f"عدد المفاتيح المحفوظة: {keys_count}",
            style="Modern.TLabel",
        )
        self.keys_label.pack(fill="x", padx=5)

        # إطار قائمة البروفايلات
        profiles_frame = ttk.LabelFrame(
            main_frame,
            text="البروفايلات المتوفرة",
            style="Modern.TLabelframe",
            padding="10",
        )
        profiles_frame.pack(fill="both", expand=True, pady=(0, 10))

        # إنشاء قائمة البروفايلات مع شريط التمرير
        profiles_list_frame = ttk.Frame(profiles_frame)
        profiles_list_frame.pack(fill="both", expand=True)

        # شريط التمرير
        scrollbar = ttk.Scrollbar(profiles_list_frame)
        scrollbar.pack(side="right", fill="y")

        # قائمة البروفايلات
        profiles_listbox = tk.Listbox(
            profiles_list_frame,
            bg="#2d2d2d",
            fg="white",
            selectmode="single",
            yscrollcommand=scrollbar.set,
        )
        profiles_listbox.pack(side="left", fill="both", expand=True)
        scrollbar.config(command=profiles_listbox.yview)

        # تحديث قائمة البروفايلات
        for profile in sorted(self.profiles.keys()):
            profiles_listbox.insert("end", profile)
            if profile == self.current_profile:
                profiles_listbox.selection_set("end")

        # إطار الأزرار
        buttons_frame = ttk.Frame(main_frame, style="Modern.TFrame")
        buttons_frame.pack(fill="x", pady=10)

        def create_new_profile():
            """إنشاء بروفايل جديد"""
            name = simpledialog.askstring("إنشاء بروفايل", "أدخل اسم البروفايل الجديد:")
            if name:
                name = name.strip()
                if not name:
                    self.log_comment("❌ الرجاء إدخال اسم صحيح للبروفايل")
                    return
                if name in self.profiles:
                    self.log_comment("❌ هذا البروفايل موجود مسبقاً")
                    return

                # إنشاء بروفايل جديد
                self.profiles[name] = {"bindings": {}}
                self.current_profile = name
                self.key_bindings = {}
                self.save_profiles()

                # تحديث القائمة
                profiles_listbox.insert("end", name)
                profiles_listbox.selection_clear(0, "end")
                profiles_listbox.selection_set("end")

                # تحديث العرض
                current_profile_label.config(text=f"البروفايل النشط: {name}")
                self.keys_label.config(text="عدد المفاتيح المحفوظة: 0")

                self.log_comment(f"✅ تم إنشاء البروفايل: {name}")

        def load_selected_profile():
            """تحميل البروفايل المحدد"""
            selection = profiles_listbox.curselection()
            if not selection:
                self.log_comment("⚠️ الرجاء تحديد بروفايل")
                return

            profile_name = profiles_listbox.get(selection[0])
            if profile_name in self.profiles:
                # حفظ البروفايل الحالي قبل التغيير
                if self.current_profile:
                    self.profiles[self.current_profile] = {
                        "bindings": self.key_bindings.copy()
                    }

                # تحميل البروفايل الجديد
                self.key_bindings = (
                    self.profiles[profile_name].get("bindings", {}).copy()
                )
                self.current_profile = profile_name
                self.save_profiles()
                self.update_keys_display()

                # تحديث العرض
                current_profile_label.config(text=f"البروفايل النشط: {profile_name}")
                keys_count = len(self.key_bindings)
                self.keys_label.config(text=f"عدد المفاتيح المحفوظة: {keys_count}")

                self.log_comment(f"✅ تم تحميل البروفايل: {profile_name}")

        def delete_selected_profile():
            """حذف البروفايل المحدد"""
            selection = profiles_listbox.curselection()
            if not selection:
                self.log_comment("⚠️ الرجاء تحديد بروفايل للحذف")
                return

            profile_name = profiles_listbox.get(selection[0])
            if messagebox.askyesno(
                "تأكيد الحذف", f"هل أنت متأكد من حذف البروفايل '{profile_name}'؟"
            ):
                if profile_name == self.current_profile:
                    self.current_profile = None
                    self.key_bindings = {}
                    self.update_keys_display()
                    current_profile_label.config(text="البروفايل النشط: لا يوجد")
                    self.keys_label.config(text="عدد المفاتيح المحفوظة: 0")

                del self.profiles[profile_name]
                self.save_profiles()
                profiles_listbox.delete(selection[0])
                self.log_comment(f"✅ تم حذف البروفايل: {profile_name}")

        def import_profiles():
            """استيراد البروفايلات"""
            file_path = filedialog.askopenfilename(
                filetypes=[("ملفات البروفايلات", "*.json"), ("جميع الملفات", "*.*")],
                title="استيراد البروفايلات",
            )
            if file_path:
                try:
                    with open(file_path, "r", encoding="utf-8") as f:
                        imported_profiles = json.load(f)

                    # التحقق من صحة البيانات المستوردة
                    if not isinstance(imported_profiles, dict):
                        raise ValueError("تنسيق الملف غير صحيح")

                    # دمج البروفايلات المستوردة مع الحالية
                    self.profiles.update(imported_profiles)
                    self.save_profiles()

                    # تحديث القائمة
                    profiles_listbox.delete(0, "end")
                    for profile in sorted(self.profiles.keys()):
                        profiles_listbox.insert("end", profile)

                    self.log_comment("✅ تم استيراد البروفايلات بنجاح")
                except Exception as e:
                    self.log_comment(f"❌ خطأ في استيراد البروفايلات: {str(e)}")

        def export_profiles():
            """تصدير البروفايلات"""
            if not self.profiles:
                self.log_comment("⚠️ لا توجد بروفايلات للتصدير")
                return

            file_path = filedialog.asksaveasfilename(
                defaultextension=".json",
                filetypes=[("ملفات البروفايلات", "*.json"), ("جميع الملفات", "*.*")],
                title="تصدير البروفايلات",
            )
            if file_path:
                try:
                    with open(file_path, "w", encoding="utf-8") as f:
                        json.dump(self.profiles, f, ensure_ascii=False, indent=4)
                    self.log_comment("✅ تم تصدير البروفايلات بنجاح")
                except Exception as e:
                    self.log_comment(f"❌ خطأ في تصدير البروفايلات: {str(e)}")

        # أزرار التحكم
        ModernButton(
            buttons_frame,
            text="➕ إنشاء",
            command=create_new_profile,
            style_name="Modern.TButton",
        ).pack(side="left", padx=5)

        ModernButton(
            buttons_frame,
            text="📂 تحميل",
            command=load_selected_profile,
            style_name="Modern.TButton",
        ).pack(side="left", padx=5)

        ModernButton(
            buttons_frame,
            text="❌ حذف",
            command=delete_selected_profile,
            style_name="Modern.TButton",
        ).pack(side="left", padx=5)

        ModernButton(
            buttons_frame,
            text="📥 استيراد",
            command=import_profiles,
            style_name="Modern.TButton",
        ).pack(side="left", padx=5)

        ModernButton(
            buttons_frame,
            text="📤 تصدير",
            command=export_profiles,
            style_name="Modern.TButton",
        ).pack(side="left", padx=5)

        ModernButton(
            buttons_frame,
            text="✖️ إغلاق",
            command=dialog.destroy,
            style_name="Modern.TButton",
        ).pack(side="right", padx=5)

        # جعل النافذة مركزية
        dialog.transient(self.root)
        dialog.grab_set()
        dialog.focus()

    def start_video_server(self):
        if self.video_server is None:
            handler = SimpleHTTPRequestHandler
            self.video_server = socketserver.TCPServer(("", self.video_port), handler)
            server_thread = threading.Thread(target=self.video_server.serve_forever)
            server_thread.daemon = True
            server_thread.start()
            self.video_url = f"http://localhost:{self.video_port}/video.html"
            self.log_comment(f"تم بدء خادم الفيديو على: {self.video_url}")

    def stop_video_server(self):
        if self.video_server:
            self.video_server.shutdown()
            self.video_server.server_close()
            self.video_server = None
            self.video_url = None
            self.log_comment("تم إيقاف خادم الفيديو")

    def create_video_html(self, video_path):
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>TikTok Live Video</title>
            <style>
                body {{ margin: 0; padding: 0; overflow: hidden; }}
                video {{ width: 100%; height: 100%; object-fit: cover; }}
            </style>
        </head>
        <body>
            <video id="videoPlayer" autoplay loop>
                <source src="{os.path.basename(video_path)}" type="video/mp4">
                Your browser does not support the video tag.
            </video>
            <script>
                const video = document.getElementById('videoPlayer');
                function playVideo() {{
                    video.play();
                }}
                function stopVideo() {{
                    video.pause();
                    video.currentTime = 0;
                }}
                window.addEventListener('message', function(event) {{
                    if (event.data === 'play') {{
                        playVideo();
                    }} else if (event.data === 'stop') {{
                        stopVideo();
                    }}
                }});
            </script>
        </body>
        </html>
        """
        with open("video.html", "w") as f:
            f.write(html_content)

    def start_media_server(self):
        if self.media_server is None:
            handler = SimpleHTTPRequestHandler
            self.media_server = socketserver.TCPServer(("", self.media_port), handler)
            server_thread = threading.Thread(target=self.media_server.serve_forever)
            server_thread.daemon = True
            server_thread.start()
            self.media_url = f"http://localhost:{self.media_port}/media.html"
            self.log_comment(f"تم بدء خادم الوسائط على: {self.media_url}")

    def stop_media_server(self):
        if self.media_server:
            self.media_server.shutdown()
            self.media_server.server_close()
            self.media_server = None
            self.media_url = None
            self.log_comment("تم إيقاف خادم الوسائط")

    def create_media_html(self, media_path, hide_initially):
        file_extension = os.path.splitext(media_path)[1].lower()
        is_video = file_extension in ['.mp4', '.webm']
        is_image = file_extension in ['.gif', '.png', '.jpg', '.jpeg', '.webp']

        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>TikTok Live Media</title>
            <style>
                body {{ margin: 0; padding: 0; overflow: hidden; }}
                #mediaContainer {{ width: 100%; height: 100%; display: {"none" if hide_initially else "flex"}; justify-content: center; align-items: center; }}
                {"video" if is_video else "img"} {{ max-width: 100%; max-height: 100%; object-fit: contain; }}
            </style>
        </head>
        <body>
            <div id="mediaContainer">
                {"<video id='mediaPlayer' autoplay loop>" if is_video else "<img id='mediaPlayer'>"}
                    {"<source src='" + os.path.basename(media_path) + "' type='video/" + file_extension[1:] + "'>" if is_video else ""}
                {"</video>" if is_video else ""}
            </div>
            <script>
                const mediaContainer = document.getElementById('mediaContainer');
                const mediaPlayer = document.getElementById('mediaPlayer');
                {"" if is_video else "mediaPlayer.src = '" + os.path.basename(media_path) + "';"}
                function showMedia() {{
                    mediaContainer.style.display = 'flex';
                    {"mediaPlayer.play();" if is_video else ""}
                }}
                function hideMedia() {{
                    mediaContainer.style.display = 'none';
                    {"mediaPlayer.pause(); mediaPlayer.currentTime = 0;" if is_video else ""}
                }}
                window.addEventListener('message', function(event) {{
                    if (event.data === 'show') {{
                        showMedia();
                    }} else if (event.data === 'hide') {{
                        hideMedia();
                    }}
                }});
            </script>
        </body>
        </html>
        """
        with open("media.html", "w") as f:
            f.write(html_content)

    def show_media(self, media_path, duration):
        if not self.media_url:
            self.create_media_html(media_path, False)
            self.start_media_server()
        
        webbrowser.open(self.media_url)
        # إخفاء الوسائط بعد المدة المحددة
        self.root.after(duration, lambda: self.hide_media())

    def hide_media(self):
        if self.media_url:
            # إرسال رسالة لإخفاء الوسائط
            # يمكنك استخدام selenium أو أي طريقة أخرى لإرسال رسالة JavaScript للصفحة
            pass


if __name__ == "__main__":
    root = tk.Tk()
    app = TikTokLiveGUI(root)
    root.mainloop()
