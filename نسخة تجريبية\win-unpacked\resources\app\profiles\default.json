{"giftMappings": [{"id": "1750504759691", "mappingName": "kick", "eventType": "gift", "actions": ["sound", "image", "keypress"], "duration": 1, "overlayId": "1", "giftId": "5655", "giftName": "", "giftImage": "", "condition": "any", "keypressSequence": [{"key": "x", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 500}], "mediaFilePath": "/uploads/1751630713972-Dragon Ball Sticker by Toei Animation.gif", "soundFilePath": "/uploads/1751630708623-da.mp3", "playSoundOnServer": false}, {"id": "1750505308497", "mappingName": "kick 2", "eventType": "gift", "actions": ["sound", "image", "keypress"], "duration": 1, "overlayId": "2", "giftId": "5269", "giftName": "", "giftImage": "", "condition": "any", "keypressSequence": [{"key": "x", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 500}], "mediaFilePath": "/uploads/1751631409798-Whip It Good Stop Motion Sticker by Mighty Oak.gif", "soundFilePath": "/uploads/1751631405011-crack_the_whip.mp3", "playSoundOnServer": false}, {"id": "1750505595995", "mappingName": "kick 3", "eventType": "gift", "actions": ["sound", "image", "keypress"], "duration": 2, "overlayId": "3", "giftId": "5487", "giftName": "", "giftImage": "", "condition": "any", "keypressSequence": [{"key": "x", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 200}, {"key": "x", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 200}, {"key": "x", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 200}, {"key": "x", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 200}, {"key": "x", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 400}], "mediaFilePath": "/uploads/1751631428731-nghiep.gif", "soundFilePath": "/uploads/1751631423167-nghiep.mp3", "playSoundOnServer": false}, {"id": "1750505954353", "mappingName": "kick 4", "eventType": "gift", "actions": ["sound", "image", "keypress", "user-display"], "duration": 3, "overlayId": "4", "giftId": "5658", "giftName": "", "giftImage": "", "condition": "any", "keypressSequence": [{"key": "x", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 500}, {"key": "x", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 200}, {"key": "x", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 200}, {"key": "x", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 200}, {"key": "x", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 200}, {"key": "x", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 200}, {"key": "x", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 200}, {"key": "x", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 200}, {"key": "x", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 200}], "mediaFilePath": "/uploads/1751631458052-songoku.gif", "soundFilePath": "/uploads/1751631442783-songoku.mp3", "playSoundOnServer": false}, {"id": "1750506237066", "mappingName": "kick 5", "eventType": "gift", "actions": ["sound", "image", "keypress", "user-display"], "duration": 7, "overlayId": "5", "giftId": "5879", "giftName": "", "giftImage": "", "condition": "any", "keypressSequence": [{"key": "x", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 0}, {"key": "x", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 100}, {"key": "x", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 100}, {"key": "x", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 100}, {"key": "x", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 100}, {"key": "x", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 100}, {"key": "x", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 100}, {"key": "x", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 100}, {"key": "x", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 100}, {"key": "x", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 100}, {"key": "x", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 100}, {"key": "x", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 100}, {"key": "x", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 100}, {"key": "x", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 100}, {"key": "x", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 100}, {"key": "x", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 100}, {"key": "x", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 100}, {"key": "x", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 100}, {"key": "x", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 100}, {"key": "x", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 100}, {"key": "x", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 100}, {"key": "x", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 100}, {"key": "x", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 100}, {"key": "x", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 200}, {"key": "x", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 200}, {"key": "x", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 200}, {"key": "x", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 200}, {"key": "x", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 300}, {"key": "x", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 300}, {"key": "x", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 300}, {"key": "x", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 300}, {"key": "x", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 300}, {"key": "x", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 300}, {"key": "x", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 300}, {"key": "x", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 300}, {"key": "x", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 400}, {"key": "x", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 400}], "mediaFilePath": "/uploads/1751631486229-metal-slug-soldier.gif", "soundFilePath": "/uploads/1751631477999-sungmay.mp3", "playSoundOnServer": false}, {"id": "1750507459588", "mappingName": "help", "eventType": "gift", "actions": ["sound", "image", "keypress", "user-display"], "duration": 20, "overlayId": "6", "giftId": "5659", "giftName": "", "giftImage": "", "condition": "any", "keypressSequence": [{"key": "space", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": true, "holdDuration": 20000, "isCustom": false, "delay": 0}], "mediaFilePath": "/uploads/1751631508412-<PERSON> Wings Sticker by Phat Kandi.gif", "soundFilePath": "/uploads/1751631504566-fly-me-to-the-moon.mp3", "playSoundOnServer": false}, {"id": "1750515030420", "mappingName": "help 2", "eventType": "gift", "actions": ["sound", "image", "keypress", "user-display"], "duration": 40, "overlayId": "7", "giftId": "6267", "giftName": "", "giftImage": "", "condition": "any", "keypressSequence": [{"key": "space", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": true, "holdDuration": 40000, "isCustom": false, "delay": 0}], "mediaFilePath": "/uploads/1751631530490-Angel Wings Baby Sticker by Nuby USA.gif", "soundFilePath": "/uploads/1751631526298-tom-jones-its-not-unusual-11.mp3", "playSoundOnServer": false}, {"id": "1750517783570", "mappingName": "<PERSON><PERSON>", "eventType": "gift", "actions": ["sound", "image", "keypress", "user-display"], "duration": 44, "overlayId": "8", "giftId": "6820", "giftName": "", "giftImage": "", "condition": "any", "keypressSequence": [{"key": "k", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 0}], "mediaFilePath": "/uploads/1751631553450-3D Skull Sticker by badblueprints.gif", "soundFilePath": "/uploads/1751631548511-wide putin walking but its fnaf full version  fnaf-[AudioTrimmer (mp3cut.net).mp3", "playSoundOnServer": false}, {"id": "1750518346921", "mappingName": "end game", "eventType": "gift", "actions": ["sound", "image", "user-display"], "duration": 51, "overlayId": "9", "giftId": "6369", "giftName": "", "giftImage": "", "condition": "any", "mediaFilePath": "/uploads/1751631579292-3D Skull Sticker by badblueprints (1).gif", "soundFilePath": "/uploads/1751631575723-causmic-flight-to-tunisia-[AudioTrimmer.com](1).mp3", "playSoundOnServer": false}, {"id": "1751633556362", "mappingName": "جميري2", "eventType": "join", "actions": ["video"], "duration": 13, "overlayId": "11", "joinType": "specific", "specificUsername": "ax9_890", "eventDetails": "عند انضمام المستخدم: ax9_890", "condition": "any", "mediaFilePath": "/uploads/1751633526631-yt1z.net-ImRich-BatmanPattisonBaleAffleckEditTimeless-TheWeekndPlayboiCartibatman1080p60-ezgif.com-video-cutter.mp4", "playSoundOnServer": false}, {"id": "1751633653647", "mappingName": "ايه", "eventType": "join", "actions": ["video"], "duration": 14, "overlayId": "10", "joinType": "specific", "specificUsername": "lolo_2.255", "eventDetails": "عند انضمام المستخدم: lolo_2.255", "condition": "any", "mediaFilePath": "/uploads/1752225176284-aya.mp4", "playSoundOnServer": false}, {"id": "1751634018961", "mappingName": "تخريب", "eventType": "like", "actions": ["sound", "image", "keypress"], "duration": 1, "overlayId": "2", "likeCount": 50, "eventDetails": "عند الوصول إلى 50 إعجاب", "condition": "any", "keypressSequence": [{"key": "x", "modifiers": {"ctrl": false, "alt": false, "shift": false}, "holdKey": false, "holdDuration": 0, "isCustom": true, "delay": 0}], "mediaFilePath": "/uploads/1751634007351-Whip It Good Stop Motion Sticker by Mighty Oak.gif", "soundFilePath": "/uploads/1751633999450-crack_the_whip.mp3", "playSoundOnServer": false, "lastProcessedLikeCounts": {"lyes.abdelhamid": 4000, "amaurdz28": 0, "ahmed_lbeshbeshy": 8900, "eslam_fathe1": 1350, "muhammadzakaria109": 0, "eyadmohmed94": 0, "user4722692408336": 150, "bondkaa.100": 0, "xa9_k": 0, "adhamalgaml": 50, "m.karam.999": 0, "hss.am403": 0, "firooofiroo": 50, "z_______________e": 4700, "alisaprri": 0, "hoda59799": 0, "tt92286": 100, "mahamou047": 150, "kotkot818": 0, "kaboos8374": 350, "adhamlattr": 350, "_.snake._16": 0, "akshdebnath839": 0, "user3309745242920": 50, "user3593680653490": 250, "frogmk4": 1800, "mohanadmohanad450": 0, "user28532857048508": 100, "swara.wshyar": 0, "younis.ahmed295": 0, "karmomr7": 1850, "laza5524": 6850, "611i9": 1100, "mazen42823": 2100, "hishamabdelazeim": 100, "user523083540346": 100, "hmny93": 2700, "yassinmohamed6139": 150, "y_6ik1": 400, "yahyayasser136": 0, "user5121790929867": 100, "user7812487580594": 0, "souissimedrayen": 0, "mona.mohamed561": 0, "mouaid_dib51": 150, "yassinhijazi5": 100, "haleem_31": 0, "ade7_reda_1": 0}}], "displaySettings": {"showUsernames": true, "showGiftNames": true, "showGiftImages": true, "showDiamondCount": true, "alertPosition": "center", "alertTheme": "default", "alertSize": "normal", "alertDuration": 5, "showBigGifts": true, "showFollows": false, "showLikes": false, "showComments": false, "backgroundColor": "#000000", "textColor": "#ffffff", "accentColor": "#ff3b5c", "fontFamily": "'<PERSON><PERSON><PERSON>', sans-serif", "fontSize": 16, "enableAnimations": false, "animationType": "shake", "width": 30, "opacity": 80, "backgroundOpacity": 0, "maxItems": 5, "customPosition": {"x": 10, "y": 10}, "sound": {"enabled": false, "volume": 50}}, "ttsSettings": {"enabled": true, "voice": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "speed": 1.1, "volume": 51, "minLength": 1, "maxLength": 500, "readRate": 2, "blockedWords": [], "models": [{"id": "ar-EG-SalmaNeural", "name": "العربية (مصر) - سلمى (أنثى)"}, {"id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "العربية (مصر) - <PERSON><PERSON><PERSON><PERSON> (ذكر)"}, {"id": "ar-SA-HamedNeural", "name": "العربية (السعودية) - حا<PERSON><PERSON> (ذكر)"}, {"id": "ar-SA-ZariyahNeural", "name": "العربية (السعودية) - زارية (أنثى)"}, {"id": "ar-AE-FatimaNeural", "name": "العربية (الإمارات) - فاطمة (أنثى)"}, {"id": "ar-AE-HamdanNeural", "name": "العربية (الإمارات) - ح<PERSON><PERSON><PERSON> (ذكر)"}, {"id": "ar-DZ-AminaNeural", "name": "العربية (الجزائر) - أمينة (أنثى)"}, {"id": "ar-DZ-Ismael<PERSON>eural", "name": "العربية (الجزائر) - إس<PERSON><PERSON><PERSON><PERSON><PERSON> (ذكر)"}, {"id": "ar-BH-AliNeural", "name": "العربية (البحرين) - علي (ذكر)"}, {"id": "ar-BH-<PERSON><PERSON>eural", "name": "العربية (البحرين) - ليلى (أنثى)"}, {"id": "ar-IQ-BasselNeural", "name": "العربية (العراق) - با<PERSON><PERSON> (ذكر)"}, {"id": "a<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "العربية (العراق) - رنا (أنثى)"}, {"id": "ar-JO-SanaNeural", "name": "العربية (الأردن) - سناء (أنثى)"}, {"id": "ar-JO-TaimNeural", "name": "العربية (الأردن) - تيم (ذكر)"}, {"id": "ar-KW-FahedNeural", "name": "العربية (الكويت) - فهد (ذكر)"}, {"id": "ar-KW-NouraNeural", "name": "العربية (الكويت) - نورة (أنثى)"}, {"id": "ar-LB-<PERSON><PERSON><PERSON><PERSON>", "name": "العربية (لبنان) - ليلى (أنثى)"}, {"id": "ar-LB-RamiNeural", "name": "العربية (لبنان) - را<PERSON><PERSON> (ذكر)"}, {"id": "ar-LY-ImanNeural", "name": "العربية (ليبيا) - <PERSON><PERSON><PERSON><PERSON> (أن<PERSON>ى)"}, {"id": "ar-<PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>", "name": "العربية (ليبيا) - <PERSON><PERSON><PERSON> (ذ<PERSON><PERSON>)"}, {"id": "ar-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "العربية (المغرب) - ج<PERSON><PERSON><PERSON> (ذكر)"}, {"id": "ar-<PERSON><PERSON><PERSON><PERSON>N<PERSON><PERSON>", "name": "العربية (المغرب) - من<PERSON> (أنثى)"}, {"id": "ar-O<PERSON><PERSON>Abdullah<PERSON><PERSON><PERSON>", "name": "العربية (عمان) - ع<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (ذكر)"}, {"id": "ar-OM-AyshaNeural", "name": "العربية (عمان) - عائشة (أنثى)"}, {"id": "ar-QA-AmalNeural", "name": "العربية (قطر) - <PERSON><PERSON><PERSON> (أنثى)"}, {"id": "ar-QA-MoazNeural", "name": "العربية (قطر) - معاذ (ذكر)"}, {"id": "ar-SY-AmanyNeural", "name": "العربية (سوريا) - أم<PERSON>ي (أنثى)"}, {"id": "ar-S<PERSON>-<PERSON>thNeural", "name": "العربية (سوريا) - ليث (ذكر)"}, {"id": "ar-TN-HediNeural", "name": "العربية (تونس) - هادي (ذكر)"}, {"id": "ar-TN-ReemNeural", "name": "العربية (تونس) - ريم (أنثى)"}, {"id": "ar-<PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "العربية (اليمن) - مريم (أنثى)"}, {"id": "ar-YE-SalehNeural", "name": "العربية (اليمن) - صال<PERSON> (ذكر)"}, {"id": "en-US-AriaNeural", "name": "الإنجليزية (أمريكية) - آريا (أنثى)"}, {"id": "en-US-GuyN<PERSON><PERSON>", "name": "الإنجليزية (أمريكية) - جاي (ذكر)"}, {"id": "en-US-<PERSON><PERSON><PERSON><PERSON>", "name": "الإنجليزية (أمريكية) - جيني (أنثى)"}, {"id": "en-GB-<PERSON><PERSON><PERSON><PERSON>", "name": "الإنجليزية (بريطانية) - سونيا (أنثى)"}, {"id": "en-GB-RyanN<PERSON><PERSON>", "name": "الإنجليزية (بريطانية) - <PERSON><PERSON><PERSON><PERSON> (ذكر)"}, {"id": "fr-FR-<PERSON><PERSON>", "name": "الفرنسية - دينيز (أنثى)"}, {"id": "fr-FR-<PERSON><PERSON><PERSON><PERSON>", "name": "الفرنسية - <PERSON><PERSON><PERSON><PERSON> (ذك<PERSON>)"}, {"id": "de-DE-Katja<PERSON>eural", "name": "الألمانية - كاتيا (أنثى)"}, {"id": "de-DE-ConradNeural", "name": "الألمانية - كونر<PERSON> (ذكر)"}, {"id": "es-ES-Elvira<PERSON>eural", "name": "الإسبانية - إلفيرا (أنثى)"}, {"id": "es-ES-AlvaroNeural", "name": "الإسبانية - أ<PERSON><PERSON><PERSON><PERSON><PERSON> (ذكر)"}]}}